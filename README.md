# HTTP负载测试工具

一个高性能的Java HTTP负载测试工具，能够支持每分钟100,000+请求（约1,667+ RPS），基于异步非阻塞架构设计。

## 特性

### 🚀 高性能
- **异步非阻塞**: 基于OkHttp的异步HTTP客户端
- **连接池管理**: 智能连接复用和Keep-Alive支持
- **精确速率控制**: 支持精确的RPS控制
- **高并发**: 可配置的并发级别，支持数千并发连接

### 📊 丰富的监控指标
- **实时统计**: 当前RPS、总请求数、成功/失败率
- **响应时间分析**: 最小值、最大值、平均值、P95、P99等百分位数
- **错误分类**: 连接错误、超时错误、HTTP错误等详细分类
- **连接池监控**: 活跃连接数、空闲连接数统计

### ⚙️ 灵活配置
- **多种HTTP方法**: 支持GET、POST、PUT、DELETE、HEAD、PATCH
- **自定义请求**: 可配置请求头、请求体、Content-Type
- **测试控制**: 支持按时间或请求数量控制测试
- **超时配置**: 可配置连接超时和读取超时

### 📈 详细报告
- **实时显示**: 定期显示测试进度和统计信息
- **最终报告**: 测试完成后显示详细的统计分析
- **CSV导出**: 支持将统计数据导出为CSV文件

## 快速开始

### 编译项目

```bash
# 克隆项目
git clone <repository-url>
cd tools_java

# 编译项目
mvn clean compile

# 运行测试
mvn test

# 打包
mvn package
```

### 基本使用

```bash
# 基本GET请求测试
java -cp target/classes com.example.loadtest.LoadTestMain -u http://example.com

# 指定RPS和并发数
java -cp target/classes com.example.loadtest.LoadTestMain \
  -u http://example.com \
  -r 2000 \
  -c 200 \
  -d 300

# POST请求测试
java -cp target/classes com.example.loadtest.LoadTestMain \
  -u http://api.example.com/users \
  -m POST \
  -H "Content-Type: application/json" \
  -b '{"name":"test","email":"<EMAIL>"}' \
  -r 1500 \
  -c 150

# 保存结果到文件
java -cp target/classes com.example.loadtest.LoadTestMain \
  -u http://example.com \
  -o results.csv \
  -r 1000 \
  -d 600
```

## 命令行参数

### 基本参数
- `-u, --url <URL>`: 目标URL（必需）
- `-m, --method <METHOD>`: HTTP方法（默认：GET）
- `-r, --rps <NUMBER>`: 每秒请求数（默认：1000）
- `-d, --duration <SECONDS>`: 测试持续时间（默认：60秒）
- `-n, --requests <NUMBER>`: 总请求数（如果指定，将忽略duration）
- `-c, --concurrency <NUMBER>`: 并发数（默认：100）

### HTTP配置
- `-H, --header <HEADER>`: 添加HTTP头部（格式：'Key: Value'）
- `-b, --body <BODY>`: 请求体内容
- `-t, --content-type <TYPE>`: Content-Type头部（默认：application/json）

### 连接配置
- `--connect-timeout <SECONDS>`: 连接超时时间（默认：30秒）
- `--read-timeout <SECONDS>`: 读取超时时间（默认：30秒）
- `--max-connections <NUMBER>`: 最大总连接数（默认：1000）
- `--max-connections-per-host <NUMBER>`: 每个主机的最大连接数（默认：200）
- `--no-keepalive`: 禁用Keep-Alive连接

### 报告配置
- `--report-interval <SECONDS>`: 报告间隔（默认：5秒）
- `-o, --output <FILE>`: 输出文件路径（CSV格式）
- `--no-detailed-metrics`: 禁用详细指标显示

### 其他
- `-h, --help`: 显示帮助信息
- `-v, --version`: 显示版本信息

## 使用示例

### 1. 基本负载测试
```bash
java -cp target/classes com.example.loadtest.LoadTestMain \
  -u https://httpbin.org/get \
  -r 500 \
  -c 50 \
  -d 120
```

### 2. API接口测试
```bash
java -cp target/classes com.example.loadtest.LoadTestMain \
  -u https://jsonplaceholder.typicode.com/posts \
  -m POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -b '{"title":"test","body":"test content","userId":1}' \
  -r 1000 \
  -c 100 \
  -d 300
```

### 3. 高并发压力测试
```bash
java -cp target/classes com.example.loadtest.LoadTestMain \
  -u http://your-api.com/endpoint \
  -r 2000 \
  -c 300 \
  -d 600 \
  --max-connections 2000 \
  --max-connections-per-host 500 \
  -o high-load-test.csv
```

### 4. 固定请求数测试
```bash
java -cp target/classes com.example.loadtest.LoadTestMain \
  -u http://example.com \
  -n 10000 \
  -r 1500 \
  -c 200
```

## 输出示例

### 实时输出
```
================================================================================
HTTP负载测试开始
================================================================================
目标URL: https://httpbin.org/get
HTTP方法: GET
目标RPS: 1000
并发数: 100
测试持续时间: 60秒
开始时间: 2024-01-15 10:30:00
================================================================================

[2024-01-15 10:30:05] 运行时间: 5s | 总请求: 5000 | 成功: 4998 | 失败: 2 | 当前RPS: 1000.2 | 平均RPS: 1000.0
    响应时间 - 最小: 45.2ms | 平均: 123.5ms | 最大: 456.7ms | P95: 234.1ms | P99: 345.6ms
    成功率: 99.96% | 数据传输: 12.34MB | 连接池状态: 总连接=100, 空闲=20, 活跃=80

[2024-01-15 10:30:10] 运行时间: 10s | 总请求: 10000 | 成功: 9995 | 失败: 5 | 当前RPS: 999.8 | 平均RPS: 1000.0
    响应时间 - 最小: 42.1ms | 平均: 125.3ms | 最大: 567.8ms | P95: 245.2ms | P99: 378.9ms
    成功率: 99.95% | 数据传输: 24.67MB | 连接池状态: 总连接=100, 空闲=15, 活跃=85
```

### 最终报告
```
================================================================================
负载测试完成 - 最终统计报告
================================================================================
测试概要:
  总运行时间: 60秒
  总请求数: 60000
  成功请求: 59850
  失败请求: 150
  成功率: 99.75%
  平均RPS: 1000.00
  数据传输总量: 148.25MB

响应时间统计:
  最小值: 38.5ms
  最大值: 2345.6ms
  平均值: 124.7ms
  P50 (中位数): 115.2ms
  P90: 189.3ms
  P95: 234.5ms
  P99: 456.7ms
  P99.9: 1234.5ms

错误统计:
  连接错误: 45
  超时错误: 78
  HTTP错误: 23
  其他错误: 4
================================================================================
```

## 性能调优建议

### 1. JVM参数优化
```bash
java -Xmx4g -Xms4g -XX:+UseG1GC -XX:MaxGCPauseMillis=100 \
  -cp target/classes com.example.loadtest.LoadTestMain \
  -u http://example.com -r 2000 -c 300
```

### 2. 系统级优化
- 增加文件描述符限制：`ulimit -n 65536`
- 调整TCP参数以支持更多连接
- 确保网络带宽充足

### 3. 配置建议
- **高RPS场景**: 增加并发数和连接池大小
- **长时间测试**: 适当增加超时时间
- **大响应体**: 调整读取超时时间

## 技术架构

- **HTTP客户端**: OkHttp 4.x（异步非阻塞）
- **统计引擎**: HdrHistogram（高精度响应时间统计）
- **并发控制**: Java并发包（线程池、信号量）
- **命令行解析**: Apache Commons CLI

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个工具。
