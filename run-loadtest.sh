#!/bin/bash

# HTTP负载测试工具启动脚本 (Linux/Mac版本)

set -e

echo ""
echo "================================================================================"
echo "                           HTTP负载测试工具 v1.0.0"
echo "================================================================================"
echo "高性能HTTP负载测试工具，支持每分钟100,000+请求"
echo ""

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "[错误] 未找到Java环境"
    echo "请确保已安装Java 8或更高版本并配置了PATH环境变量"
    echo ""
    exit 1
fi

# 显示Java版本
echo "[信息] 检测到Java环境:"
java -version 2>&1 | head -1
echo ""

# 创建目标目录
mkdir -p target/classes/com/example/loadtest

# 编译Java文件
echo "[信息] 正在编译负载测试工具..."
if ! javac -d target/classes src/main/java/com/example/loadtest/*.java; then
    echo "[错误] 编译失败"
    echo "请检查Java源代码是否存在语法错误"
    exit 1
fi

echo "[成功] 编译完成！"
echo ""

# 菜单函数
show_menu() {
    echo "================================================================================"
    echo "请选择操作:"
    echo "1. 运行示例测试 (推荐新手)"
    echo "2. 自定义负载测试"
    echo "3. 显示帮助信息"
    echo "4. 退出"
    echo "================================================================================"
}

# 示例测试函数
run_example() {
    echo ""
    echo "[信息] 运行示例测试..."
    echo "这将对 httpbin.org 进行低负载测试，持续30秒"
    echo ""
    
    java -Xmx1g -cp target/classes com.example.loadtest.LoadTestCLI \
        -u https://httpbin.org/get -r 20 -c 10 -d 30
    
    echo ""
    echo "[完成] 示例测试结束"
    read -p "按Enter键继续..."
}

# 自定义测试函数
run_custom() {
    echo ""
    echo "[信息] 自定义负载测试"
    echo "请输入测试参数 (直接回车使用默认值):"
    echo ""
    
    read -p "目标URL: " url
    if [ -z "$url" ]; then
        echo "[错误] URL不能为空"
        return
    fi
    
    read -p "HTTP方法 [GET]: " method
    method=${method:-GET}
    
    read -p "每秒请求数 [100]: " rps
    rps=${rps:-100}
    
    read -p "测试持续时间(秒) [60]: " duration
    duration=${duration:-60}
    
    read -p "并发数 [20]: " concurrency
    concurrency=${concurrency:-20}
    
    echo ""
    echo "[信息] 开始自定义负载测试..."
    echo "目标URL: $url"
    echo "HTTP方法: $method"
    echo "每秒请求数: $rps"
    echo "测试持续时间: ${duration}秒"
    echo "并发数: $concurrency"
    echo ""
    
    java -Xmx2g -cp target/classes com.example.loadtest.LoadTestCLI \
        -u "$url" -m "$method" -r "$rps" -d "$duration" -c "$concurrency"
    
    echo ""
    echo "[完成] 自定义测试结束"
    read -p "按Enter键继续..."
}

# 显示帮助函数
show_help() {
    echo ""
    java -cp target/classes com.example.loadtest.LoadTestCLI --help
    echo ""
    echo "================================================================================"
    echo "高级用法示例:"
    echo ""
    echo "1. 高并发测试:"
    echo "   java -Xmx4g -cp target/classes com.example.loadtest.LoadTestCLI \\"
    echo "        -u http://example.com -r 2000 -c 200 -d 300"
    echo ""
    echo "2. POST请求测试:"
    echo "   java -cp target/classes com.example.loadtest.LoadTestCLI \\"
    echo "        -u http://api.example.com/users -m POST \\"
    echo "        -H \"Content-Type: application/json\" \\"
    echo "        -b '{\"name\":\"test\"}' -r 500 -c 50"
    echo ""
    echo "3. 固定请求数测试:"
    echo "   java -cp target/classes com.example.loadtest.LoadTestCLI \\"
    echo "        -u http://example.com -n 10000 -r 1000 -c 100"
    echo ""
    echo "4. 性能调优建议:"
    echo "   - 增加JVM堆内存: -Xmx4g -Xms4g"
    echo "   - 使用G1垃圾收集器: -XX:+UseG1GC"
    echo "   - 增加文件描述符限制: ulimit -n 65536"
    echo "   - 调整TCP参数以支持更多连接"
    echo ""
    echo "================================================================================"
    read -p "按Enter键继续..."
}

# 主循环
while true; do
    show_menu
    read -p "请输入选择 (1-4): " choice
    
    case $choice in
        1)
            run_example
            ;;
        2)
            run_custom
            ;;
        3)
            show_help
            ;;
        4)
            echo ""
            echo "感谢使用HTTP负载测试工具！"
            echo ""
            exit 0
            ;;
        *)
            echo "无效选择，请重新输入"
            ;;
    esac
done
