@echo off
setlocal enabledelayedexpansion

echo.
echo ================================================================================
echo                           HTTP负载测试工具 v1.0.0
echo ================================================================================
echo 高性能HTTP负载测试工具，支持每分钟100,000+请求
echo.

REM 检查Java环境
where java >nul 2>nul
if %errorlevel% neq 0 (
    echo [错误] 未找到Java环境
    echo 请确保已安装Java 8或更高版本并配置了PATH环境变量
    echo.
    pause
    exit /b 1
)

REM 显示Java版本
echo [信息] 检测到Java环境:
java -version 2>&1 | findstr "version"
echo.

REM 创建目标目录
if not exist "target\classes\com\example\loadtest" mkdir target\classes\com\example\loadtest

REM 编译Java文件
echo [信息] 正在编译负载测试工具...
javac -d target/classes src/main/java/com/example/loadtest/*.java
if %errorlevel% neq 0 (
    echo [错误] 编译失败
    echo 请检查Java源代码是否存在语法错误
    pause
    exit /b 1
)

echo [成功] 编译完成！
echo.

REM 显示菜单
:menu
echo ================================================================================
echo 请选择操作:
echo 1. 运行示例测试 (推荐新手)
echo 2. 自定义负载测试
echo 3. 显示帮助信息
echo 4. 退出
echo ================================================================================
set /p choice="请输入选择 (1-4): "

if "%choice%"=="1" goto example
if "%choice%"=="2" goto custom
if "%choice%"=="3" goto help
if "%choice%"=="4" goto exit
echo 无效选择，请重新输入
goto menu

:example
echo.
echo [信息] 运行示例测试...
echo 这将对 httpbin.org 进行低负载测试，持续30秒
echo.
java -Xmx1g -cp target/classes com.example.loadtest.LoadTestCLI -u https://httpbin.org/get -r 20 -c 10 -d 30
echo.
echo [完成] 示例测试结束
pause
goto menu

:custom
echo.
echo [信息] 自定义负载测试
echo 请输入测试参数 (直接回车使用默认值):
echo.

set /p url="目标URL: "
if "%url%"=="" (
    echo [错误] URL不能为空
    goto custom
)

set /p method="HTTP方法 [GET]: "
if "%method%"=="" set method=GET

set /p rps="每秒请求数 [100]: "
if "%rps%"=="" set rps=100

set /p duration="测试持续时间(秒) [60]: "
if "%duration%"=="" set duration=60

set /p concurrency="并发数 [20]: "
if "%concurrency%"=="" set concurrency=20

echo.
echo [信息] 开始自定义负载测试...
echo 目标URL: %url%
echo HTTP方法: %method%
echo 每秒请求数: %rps%
echo 测试持续时间: %duration%秒
echo 并发数: %concurrency%
echo.

java -Xmx2g -cp target/classes com.example.loadtest.LoadTestCLI -u "%url%" -m %method% -r %rps% -d %duration% -c %concurrency%
echo.
echo [完成] 自定义测试结束
pause
goto menu

:help
echo.
java -cp target/classes com.example.loadtest.LoadTestCLI --help
echo.
echo ================================================================================
echo 高级用法示例:
echo.
echo 1. 高并发测试:
echo    java -Xmx4g -cp target/classes com.example.loadtest.LoadTestCLI ^
echo         -u http://example.com -r 2000 -c 200 -d 300
echo.
echo 2. POST请求测试:
echo    java -cp target/classes com.example.loadtest.LoadTestCLI ^
echo         -u http://api.example.com/users -m POST ^
echo         -H "Content-Type: application/json" ^
echo         -b "{\"name\":\"test\"}" -r 500 -c 50
echo.
echo 3. 固定请求数测试:
echo    java -cp target/classes com.example.loadtest.LoadTestCLI ^
echo         -u http://example.com -n 10000 -r 1000 -c 100
echo.
echo ================================================================================
pause
goto menu

:exit
echo.
echo 感谢使用HTTP负载测试工具！
echo.
exit /b 0
