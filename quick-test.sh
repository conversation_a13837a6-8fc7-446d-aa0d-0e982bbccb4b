#!/bin/bash

echo "快速测试HTTP负载测试工具"
echo "========================"

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java环境"
    exit 1
fi

# 创建目标目录
mkdir -p target/classes/com/example/loadtest

# 编译
echo "正在编译..."
if ! javac -d target/classes src/main/java/com/example/loadtest/*.java; then
    echo "编译失败"
    exit 1
fi

echo "编译成功！"
echo ""

# 运行快速测试
echo "运行快速测试 (5秒，低负载)..."
echo ""
java -cp target/classes com.example.loadtest.LoadTestCLI \
    -u https://httpbin.org/get -r 5 -c 3 -d 5

echo ""
echo "快速测试完成！工具运行正常。"
echo ""
echo "要运行完整的交互式界面，请执行: ./run-loadtest.sh"
