@echo off
echo 快速测试HTTP负载测试工具
echo ========================

REM 检查Java环境
where java >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到Java环境
    exit /b 1
)

REM 创建目标目录
if not exist "target\classes\com\example\loadtest" mkdir target\classes\com\example\loadtest

REM 编译
echo 正在编译...
javac -d target/classes src/main/java/com/example/loadtest/*.java
if %errorlevel% neq 0 (
    echo 编译失败
    exit /b 1
)

echo 编译成功！
echo.

REM 运行快速测试
echo 运行快速测试 (5秒，低负载)...
echo.
java -cp target/classes com.example.loadtest.LoadTestCLI -u https://httpbin.org/get -r 5 -c 3 -d 5

echo.
echo 快速测试完成！工具运行正常。
echo.
echo 要运行完整的交互式界面，请执行: run-loadtest.bat
pause
