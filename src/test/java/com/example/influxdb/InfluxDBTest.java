package com.example.influxdb;

import com.example.common.utils.InfluxDbUtils;
import org.influxdb.InfluxDB;
import org.influxdb.dto.Pong;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * Test the InfluxDB API.
 */

@RunWith(SpringRunner.class)
@SpringBootTest
public class InfluxDBTest {

	@Autowired
	InfluxDbUtils influxDbUtils;

	InfluxDB influxDB;

	@Before
	public void setUp() {
		influxDB = this.influxDbUtils.getInfluxDB();
	}

	/**
	 * Test for a ping.
	 */
	@Test
	public void testPing() {
		Pong result = this.influxDB.ping();
		Assertions.assertNotNull(result);
		Assertions.assertNotEquals(result.getVersion(), "unknown");
	}

	/**
	 * Test that version works.
	 */
	@Test
	public void testVersion() {
		String version = this.influxDB.version();
		Assertions.assertNotNull(version);
		Assertions.assertFalse(version.contains("unknown"));
	}

	/**
	 * Simple Test for a query.
	 */
	@Test
	public void testQuery() {
		QueryResult showDatabases = this.influxDB.query(new Query("show databases"));
		showDatabases.getResults().forEach(System.out::println);
	}
}
