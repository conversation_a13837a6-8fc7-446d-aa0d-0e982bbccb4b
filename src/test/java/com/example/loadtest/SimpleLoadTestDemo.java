package com.example.loadtest;

/**
 * 简单的负载测试演示
 * 不依赖外部库，用于验证核心逻辑
 */
public class SimpleLoadTestDemo {
    
    public static void main(String[] args) {
        String separator = repeatString("=", 60);
        System.out.println(separator);
        System.out.println("HTTP负载测试工具演示");
        System.out.println(separator);
        
        // 创建配置
        LoadTestConfig config = new LoadTestConfig();
        config.setTargetUrl("https://httpbin.org/get");
        config.setRequestsPerSecond(10);
        config.setConcurrency(5);
        config.setDurationSeconds(10);
        
        System.out.println("配置信息:");
        System.out.println(config);
        System.out.println();
        
        // 验证配置
        try {
            config.validate();
            System.out.println("✓ 配置验证通过");
        } catch (Exception e) {
            System.err.println("✗ 配置验证失败: " + e.getMessage());
            return;
        }
        
        // 创建统计收集器
        StatisticsCollector statisticsCollector = new StatisticsCollector();
        
        // 模拟一些请求统计
        System.out.println("\n模拟请求统计:");
        for (int i = 0; i < 20; i++) {
            if (i % 5 == 0) {
                // 模拟失败请求
                statisticsCollector.recordFailure("timeout", 5000);
                System.out.println("记录失败请求 #" + (i + 1));
            } else {
                // 模拟成功请求
                long responseTime = 100 + (long)(Math.random() * 200);
                long responseBytes = 1024 + (long)(Math.random() * 2048);
                statisticsCollector.recordSuccess(responseTime, responseBytes);
                System.out.println("记录成功请求 #" + (i + 1) + " (响应时间: " + responseTime + "ms)");
            }
            
            // 模拟请求间隔
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        // 获取统计快照
        StatisticsCollector.StatisticsSnapshot snapshot = statisticsCollector.getSnapshot();
        
        String resultSeparator = repeatString("=", 60);
        System.out.println("\n" + resultSeparator);
        System.out.println("统计结果:");
        System.out.println(resultSeparator);
        System.out.printf("总请求数: %d%n", snapshot.getTotalRequests());
        System.out.printf("成功请求: %d%n", snapshot.getSuccessfulRequests());
        System.out.printf("失败请求: %d%n", snapshot.getFailedRequests());
        System.out.printf("成功率: %.2f%%%n", snapshot.getSuccessRate());
        System.out.printf("总数据传输: %d 字节%n", snapshot.getTotalBytes());
        System.out.printf("平均RPS: %.2f%n", snapshot.getOverallRps());
        
        if (snapshot.getTotalRequests() > 0) {
            System.out.printf("最小响应时间: %.1fms%n", snapshot.getMinResponseTime());
            System.out.printf("最大响应时间: %.1fms%n", snapshot.getMaxResponseTime());
            System.out.printf("平均响应时间: %.1fms%n", snapshot.getMeanResponseTime());
            System.out.printf("P95响应时间: %.1fms%n", snapshot.getPercentile(95));
            System.out.printf("P99响应时间: %.1fms%n", snapshot.getPercentile(99));
        }
        
        System.out.println("\n错误统计:");
        System.out.printf("连接错误: %d%n", snapshot.getConnectionErrors());
        System.out.printf("超时错误: %d%n", snapshot.getTimeoutErrors());
        System.out.printf("HTTP错误: %d%n", snapshot.getHttpErrors());
        System.out.printf("其他错误: %d%n", snapshot.getOtherErrors());
        
        String endSeparator = repeatString("=", 60);
        System.out.println("\n" + endSeparator);
        System.out.println("演示完成！");
        System.out.println(endSeparator);
        
        // 测试配置的各种方法
        testConfigurationMethods();
    }
    
    private static void testConfigurationMethods() {
        System.out.println("\n测试配置方法:");
        
        LoadTestConfig config = new LoadTestConfig();
        
        // 测试HTTP方法设置
        config.setHttpMethod("post");
        System.out.println("HTTP方法设置为: " + config.getHttpMethod()); // 应该是 POST
        
        // 测试头部设置
        config.addHeader("Authorization", "Bearer token123");
        config.addHeader("Content-Type", "application/json");
        System.out.println("设置的头部数量: " + config.getHeaders().size());
        
        // 测试请求体设置
        config.setRequestBody("{\"test\": \"data\"}");
        System.out.println("请求体长度: " + config.getRequestBody().length());
        
        // 测试连接配置
        config.setConnectionTimeout(60);
        config.setReadTimeout(120);
        config.setMaxTotalConnections(2000);
        config.setKeepAlive(false);
        
        System.out.println("连接超时: " + config.getConnectionTimeout() + "秒");
        System.out.println("读取超时: " + config.getReadTimeout() + "秒");
        System.out.println("最大连接数: " + config.getMaxTotalConnections());
        System.out.println("Keep-Alive: " + config.isKeepAlive());
        
        // 测试无效配置
        LoadTestConfig invalidConfig = new LoadTestConfig();
        try {
            invalidConfig.validate();
            System.out.println("✗ 应该抛出异常但没有");
        } catch (IllegalArgumentException e) {
            System.out.println("✓ 正确捕获到配置验证异常: " + e.getMessage());
        }
        
        // 测试无效URL
        invalidConfig.setTargetUrl("invalid-url");
        try {
            invalidConfig.validate();
            System.out.println("✗ 应该抛出URL异常但没有");
        } catch (IllegalArgumentException e) {
            System.out.println("✓ 正确捕获到URL验证异常: " + e.getMessage());
        }
    }

    /**
     * 重复字符串（Java 8兼容版本）
     */
    private static String repeatString(String str, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }
}
