package com.example.loadtest;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 负载测试集成测试
 */
public class LoadTestIntegrationTest {
    
    private LoadTestConfig config;
    private StatisticsCollector statisticsCollector;
    private AsyncHttpClient httpClient;
    private MetricsReporter metricsReporter;
    private LoadTestExecutor executor;
    
    @BeforeEach
    public void setUp() {
        config = new LoadTestConfig();
        config.setTargetUrl("https://httpbin.org/get");
        config.setRequestsPerSecond(10);
        config.setConcurrency(5);
        config.setDurationSeconds(5);
        config.setReportIntervalSeconds(2);
        config.setEnableDetailedMetrics(false);
        
        statisticsCollector = new StatisticsCollector();
        httpClient = new AsyncHttpClient(config, statisticsCollector);
        metricsReporter = new MetricsReporter(statisticsCollector, httpClient, config);
        executor = new LoadTestExecutor(config, httpClient, statisticsCollector, metricsReporter);
    }
    
    @AfterEach
    public void tearDown() {
        if (executor != null) {
            executor.shutdown();
        }
    }
    
    @Test
    public void testConfigValidation() {
        LoadTestConfig invalidConfig = new LoadTestConfig();
        
        // 测试空URL
        assertThrows(IllegalArgumentException.class, invalidConfig::validate);
        
        // 测试无效URL
        invalidConfig.setTargetUrl("invalid-url");
        assertThrows(IllegalArgumentException.class, invalidConfig::validate);
        
        // 测试有效配置
        invalidConfig.setTargetUrl("https://example.com");
        assertDoesNotThrow(invalidConfig::validate);
    }
    
    @Test
    public void testStatisticsCollector() {
        // 测试成功请求记录
        statisticsCollector.recordSuccess(100, 1024);
        assertEquals(1, statisticsCollector.getTotalRequests());
        assertEquals(1, statisticsCollector.getSuccessfulRequests());
        assertEquals(0, statisticsCollector.getFailedRequests());
        assertEquals(1024, statisticsCollector.getTotalBytes());
        
        // 测试失败请求记录
        statisticsCollector.recordFailure("timeout", 5000);
        assertEquals(2, statisticsCollector.getTotalRequests());
        assertEquals(1, statisticsCollector.getSuccessfulRequests());
        assertEquals(1, statisticsCollector.getFailedRequests());
        
        // 测试统计快照
        StatisticsCollector.StatisticsSnapshot snapshot = statisticsCollector.getSnapshot();
        assertNotNull(snapshot);
        assertEquals(2, snapshot.getTotalRequests());
        assertEquals(50.0, snapshot.getSuccessRate(), 0.1);
    }
    
    @Test
    public void testHttpClientCreation() {
        assertNotNull(httpClient);
        
        // 测试连接池统计
        AsyncHttpClient.ConnectionPoolStats stats = httpClient.getConnectionPoolStats();
        assertNotNull(stats);
        assertTrue(stats.getTotalConnections() >= 0);
        assertTrue(stats.getIdleConnections() >= 0);
        assertTrue(stats.getActiveConnections() >= 0);
    }
    
    @Test
    public void testLoadTestExecutorBasicFunctionality() {
        assertFalse(executor.isRunning());
        assertEquals(0, executor.getRequestsSent());
        
        // 测试速率限制器统计
        LoadTestExecutor.RateLimiterStats rateLimiterStats = executor.getRateLimiterStats();
        assertNotNull(rateLimiterStats);
        assertTrue(rateLimiterStats.getAvailablePermits() >= 0);
        assertTrue(rateLimiterStats.getQueueLength() >= 0);
    }
    
    @Test
    public void testShortLoadTest() throws InterruptedException {
        // 配置短时间测试
        config.setDurationSeconds(2);
        config.setRequestsPerSecond(5);
        config.setConcurrency(2);
        
        // 启动测试
        executor.startTest();
        assertTrue(executor.isRunning());
        
        // 等待测试完成
        Thread.sleep(3000);
        
        // 验证测试结果
        assertFalse(executor.isRunning());
        assertTrue(executor.getRequestsSent() > 0);
        assertTrue(statisticsCollector.getTotalRequests() > 0);
        
        // 获取最终统计
        StatisticsCollector.StatisticsSnapshot finalSnapshot = statisticsCollector.getSnapshot();
        assertNotNull(finalSnapshot);
        assertTrue(finalSnapshot.getTotalRequests() > 0);
        assertTrue(finalSnapshot.getOverallRps() > 0);
    }
    
    @Test
    public void testRequestLimitTest() throws InterruptedException {
        // 配置请求数量限制测试
        config.setTotalRequests(10);
        config.setDurationSeconds(60); // 设置长时间，但应该在达到请求数后停止
        config.setRequestsPerSecond(20);
        
        // 启动测试
        executor.startTest();
        assertTrue(executor.isRunning());
        
        // 等待测试完成
        Thread.sleep(2000);
        
        // 验证测试在达到请求数后停止
        assertFalse(executor.isRunning());
        assertTrue(executor.getRequestsSent() >= 10);
    }
    
    @Test
    public void testConfigToString() {
        String configString = config.toString();
        assertNotNull(configString);
        assertTrue(configString.contains("LoadTestConfig"));
        assertTrue(configString.contains(config.getTargetUrl()));
        assertTrue(configString.contains(config.getHttpMethod()));
    }
    
    @Test
    public void testHeaderConfiguration() {
        config.addHeader("Authorization", "Bearer token123");
        config.addHeader("X-Custom-Header", "custom-value");
        
        assertEquals("Bearer token123", config.getHeaders().get("Authorization"));
        assertEquals("custom-value", config.getHeaders().get("X-Custom-Header"));
        assertTrue(config.getHeaders().containsKey("User-Agent")); // 默认header
    }
    
    @Test
    public void testPostRequestConfiguration() {
        config.setHttpMethod("POST");
        config.setRequestBody("{\"test\": \"data\"}");
        config.setContentType("application/json");
        
        assertEquals("POST", config.getHttpMethod());
        assertEquals("{\"test\": \"data\"}", config.getRequestBody());
        assertEquals("application/json", config.getContentType());
    }
    
    @Test
    public void testConnectionConfiguration() {
        config.setConnectionTimeout(60);
        config.setReadTimeout(120);
        config.setMaxTotalConnections(2000);
        config.setMaxConnectionsPerHost(500);
        config.setKeepAlive(false);
        
        assertEquals(60, config.getConnectionTimeout());
        assertEquals(120, config.getReadTimeout());
        assertEquals(2000, config.getMaxTotalConnections());
        assertEquals(500, config.getMaxConnectionsPerHost());
        assertFalse(config.isKeepAlive());
    }
}
