package com.example.tools;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.example.common.utils.FileUtil;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

public class StaticsInfoDataVo {
    private static final List<String> thirdKeys = Arrays.asList("baiHangQueryRIDS0000Service","confirmHourService","jindunRecordService","unionFailTransaction"
            ,"xinyanBlackService","yootungConsumptiveForecast","unionAdvisors","brtdhujinMultiService"
            ,"TONGDUN_DETAILS","BLACK_LIST_RESULT","hujinOperatorDurationService","preHujinService","paic_msc8624"
            ,"bxfScoreY01","duXiaoManCreditScore","puDaoLoanMultiApplyService","hzphfinFraudScoreT3","TONGDUN"
            ,"baoGuoKaService","fayanyuanLoseCreditService","DAUService","ypAssessmentService","jituiMobileStatus"
            ,"bairongService","baiweiFraudScoreService","pudaopanGuThreeElementService","fayanyuanPersonService"
            ,"baiHangNewSpecListService","bairongUserReportService","baiHangRuleResultService","baiHangPortraitService"
            ,"bairongLimitConsumptionService","nifaService","bairongCustomizeSocreToutiao","hujinOperatorStatusService"
            ,"bairongCustomizeSocre360","duXiaoManMultiloans","hicoreAntifraudScore","tongdunXexjfScoreService","bairongStrategyLoanService"
            ,"paic_msc8391","baiHangCreditService","bairongCustomizeSocreOppo","baiHangAfterCreditService","bairongUserPortrait"
            ,"queryUmengcreditScore","antiFraudVip","tongdunService","suanhuaRiskIdentCustomized","jituiMobileFactor3"
            ,"qcloudantifraudConsumptionService","ypcreditService","puDaoLianYangScoreB","jituiMobileOnline","hzphfinCreditScoreB1"
            ,"baiHangAntifraudPrdService","basicInformation","puDaoLoanCurDayApplyService","baiHangQueryFRAI001AService","ypUnitedScoreService"
            ,"baiweiScorePlusService","phoneHelpPscoreService","worldCreditRelateService","suanhuaCreditScore","tongdunscorecardService"
            ,"suanhuacreditService","jiguangaddressService","jiguangfactorService","bairongService","jingdongCustomizationService"
            ,"jingdongZrBlacklistService","userbehaviorService","tianjiLocationService","tianjiActionService","tianjiAntifraudService"
            ,"nifaService","phoneHelpService","tianjiscoreService","qcloudantifraudService","suanhuabadnessService","nfcsService"
            ,"batchPhoneHelpService","tongdunjjnlService","fayanyuanPersonService","fayanyuanCompanyService","jiaoOperatorDurationService"
            ,"jiaoOperatorStatusService","jiaoInfoValidateService","jiaoMobileDownTimeService","jiaoAttentionListService","jiaoInvolvementService"
            ,"jiaoConsumptionStageService","jiaoCashStageService","jiaoAverageConsumptionService","hujinOperator3ElementService"
            ,"hujinOperatorDurationService","hujinOperatorStatusService","hujinBankCardVerify2Service","hujinBankCardVerify3Service"
            ,"hujinBankCardVerify4Service","hujinBankCardVerify6Service","tongdunService","datavisorService","unionpaysmartService"
            ,"sougouService","tanzhiService","withShieldService","dhbcuishouService","xinyanBlackService","xinyanRadarService","integrityService"
            ,"pacraGrayscaleRecordService","pacraDunRecordService","txUnionModelService","bairongLocationService","bairongTotalloanService"
            ,"bairongDatacustService","tongdunscorecardv2Service","hujinOperatorThreeElementResMobileService","bairongFraudService","baihangBlackList"
            ,"dianhuabangcuishouService","xiaoweiOneService","xiaoweiThreeService","xiaoweiSixService","unionpaySmartRiskReportService"
            ,"ficoCreditScoreService","ronghuiFinanceAntifraudService","ronghuiFinanceMuchExponentService","baiHangCreditService","tianjiScoreV3Service"
            ,"smartConsumerDataService","iceKreditService","ronghuiGpAppService","ronghuiRrdModelService","slooptechService","xinDeWeChatService"
            ,"bairongStrategySpecialListAndFraudService","bairongStrategyLoanService","jiaoOnlineCashStageService","jiaoOnlineConsumeStageService"
            ,"jiaoPdlService","jiaoCarLoanaService","jiaoCarLoanbService","jiaoBankSceneaService","jiaoBankScenebService","tianjiScoreV6Service"
            ,"pengyuanService","bairongLimitConsumptionService","zhongqingService","jindunRecordService","ypcreditService","baiQiShiRisknameService"
            ,"baiQiShiHistoryappService","baiQiShiRelationNetWorkService","commonCreditScoreService","onlineStagingSceneScoreService"
            ,"mobileOnlineStatusService","mobileDownTimeService","qcloudantifraudConsumptionService","xindeGreyListService","fayanyuanLoseCreditService"
            ,"fayanyuanPublicModelService","zhonganminxinBadrecordService","tongdunXexjfScoreService","tongdunDexjfScoreService","iceKreditScoreService"
            ,"datavisorIpService","firstEmeContactMobileOnlineStatus","secondEmeContactMobileOnlineStatus","qcloudAntifraudV5Service"
            ,"suanhuaRiskIdentService","fulinkService","zhongIndividualInvestmentService","ucreditTongdunService","baiHangSpecialListService"
            ,"baiHangAfterCreditService","baiHangNewSpecListService","phoneHelpUnionModelService","longjingScoreService","bairongUserReportService"
            ,"baiweiFraudScoreService","baiweiScoreService","jindunRiskScoreService","jindunFraudScoreService","tianjiAntifraudServiceV2"
            ,"tianjiActionServiceV2","tianjiLocationServiceV2","tianjiScoreServiceV7","tongdunRiskService","qianyunScoreService","paic_msc8624"
            ,"paic_msc8391","paic_msc8286","paic_msc8036","zitacrDishonestyService","jituiMobileFactor3","jituiMobileStatus","jituiMobileOnline"
            ,"hhCreditData","queryUmengcreditScore","queryUmengCareerFeature","duXiaoManCreditScore","duXiaoManMultiloans","panGuBlacklist"
            ,"panGuCreditScore","witskyMobileDn","bxfScoreY01","unionAdvisors","antiFraudVip","unionFailTransaction","yootungConsumptiveForecast"
            ,"youfenService","xtmConsumptionScore","bairongUserPortrait","hicoreAntifraudScore","hicorePreLoanWarning","hicoreHighRiskCustomer"
            ,"bairongUserPortraitPreds10","baiHangAirCredit","suanhuaCreditScore","suanhuaRiskScore","nifaNewService","userPortraitMix"
            ,"ypAssessmentService","suanhuaRiskIdentCustomized","baiweiScorePlusService","bairongCustomizeSocre360","bairongCustomizeSocreToutiao"
            ,"bairongCustomizeSocreOppo","realTimeNifaService","duXiaoManRiskList","duXiaoManFraudFactor","qcloudAccessRiskService","hzphfinFraudScoreT3"
            ,"hzphfinCreditScoreB1","ypUnitedScoreService","jituiMobileStatusByEme","umengFraudSorceService","umengFraudV2Service"
            ,"iceCreditDayPupilService","iceCreditEyeService","jdReimbursementAbilityService","jdAccidentModelSectionService","blzCreditHitListService"
            ,"blzCreditTuZhi04Service","blzCreditTuZhi07Service","xinyisouFeiCui2ScoreService","xinyisouBlackListService","xinyisouLanYu2ScoreService"
            ,"xinyisouWanXiang2ScoreService","ypPayScoreService","xinyisouZiYu3ScoreService","ypCreditSinanService","pudaopanGuThreeElementService","pudaospecialOrderService","pudaoCustomerLabelService","pudaoMobileVerificationService","jituiMobileFee3","jituiMobileTalkTime","jituiMobileOnlinecV2","jituiMobileStopMachine3","jituiMobileFlow3","bxfScoreA03","qichachaSumptuaryCheckGetList","qichachaZhixingCheckGetList","qichachaShixinCheckGetList","pyHttpService","userPortraitScorelcashon","tanzhiCustomVariableScore","baiHangAfterCreditServiceNew","queryNewFRAI0001","ylBusinessBlackList","yunXingScore","querySMEIS01C","hhDZCreditData","querySMEIS01CNew","baiHangAirCreditNew","baiHangNewSpecListServiceNew","tongdunWuLiuHuaXiangService","puDaoLoanMultiApplyService","puDaoLoanCurDayApplyService","puDaoFederalLearningRiskModelService"
            ,"pudaoAccessUsualService","pudaoAntifraudConsumptionService","pudaoAntiFraudVip","puDaoLianYangScoreB","puDaoLianYangScoreA","baiHangPortraitService"
            ,"baiHangRuleResultService","baiHangAntifraudPrdService","baiHangQueryFRAI001AService","baiHangQueryRIDS0000Service","umengFinplusSmallLoanService"
            ,"aliYunAfterPayService","txLoanBehaviorUsualService","fayanyuanLoseCreditV1Service","fayanyuanPersonV1Service","tcIntegrationService"
            ,"antChainAntiFraudValueService","weiChengService","FAYANYUAN_PERSON","XINYAN_negativeBlack","hzphfinCreditScoreA1","BAIRONG","BAIRONG_STRAGY_LOAN","BAIRONG_SPECIALLIST_FRAUD","yinLianIncomeAbilityService","yinLianIncomeStabilityService","baiHangQueryModelTagService","queryIceCreditRateStratificationService","xwCacheCreditData","crmIvrService","baiHangDuxiaomanCreditScoreService","baiHangDuxiaomanMultiLoansService","xysWanXiang16394Service","xysWanXiang1646Service","address2coordService","coord2addressService","fundRecordService","dfxkzhimaService","creditCardService","dataKeyTaobaoBillService","dataKeyTaobaoReportService","dataKeyAlipayBillService","dataKeyAlipayJiaofeiService","dataKeyZhimaScoreService","datakeyTaobaoshopOrderService","datakeyTaobaoshopInfoService","datakeyTaobaoshopTransactionService","datakeyTaobaoshopAssetService","taobaoAddrRelationService");

    public static void main(String[] args) throws IOException {
        String dataStr = FileUtil.toOneLine("data.json");
        JSONObject dataVo = JSON.parseObject(dataStr);
        JSONObject thirdPartyData = dataVo.getJSONObject("thirdPartyData");
        StringBuilder keyStr = new StringBuilder();
        StringBuilder selfStr = new StringBuilder();
        for(String rootKey : dataVo.keySet()){
            keyStr.append(rootKey);
            if(!rootKey.equals("thirdPartyData")){
                selfStr.append(dataVo.getString(rootKey));
                System.out.println(rootKey);
            }
        }
        System.out.println("====================");
        StringBuilder thridStr = new StringBuilder();
        for(String thirdKey : thirdPartyData.keySet()){
            keyStr.append(thirdKey);
            if(thirdKeys.contains(thirdKey)){
                thridStr.append(thirdPartyData.getString(thirdKey));
            }else{
                System.out.println(thirdKey);
                selfStr.append(thirdPartyData.getString(thirdKey));
            }
        }
        System.out.println("总大小:  " + dataStr.length()*1.0/1024/1024 + "MB");
        System.out.println("key大小:  " + keyStr.length()*1.0/1024/1024 + "MB");
        System.out.println("自有数据： " + selfStr.length()*1.0/1024/1024 + "MB");
        System.out.println("三方数据： " + thridStr.length()*1.0/1024/1024 + "MB");
    }
}
