package com.example.tools;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Tuple;
import cn.hutool.core.text.csv.CsvData;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvRow;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.util.CharsetUtil;
import com.example.vo.FeatureItemVo;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

public class CsvFIStaticsInfo {
    private static final List<String> gpsNewstatisticsFeatureNames = Arrays.asList("hh_base_info_dr", "hh_new_selfdata_catch_dr"
            , "hh_last_gps_sd", "hh_ma_baseinfo_fests_dr");
    private static final List<String> gpsOldstatisticsFeatureNames = Arrays.asList("hh_gps_info_service_v2_sd", "hh_gps_info_service_sd"
            , "hh_test_dr", "hh_gps_city_salary_sd","hh_gps_city_salary_dr","VERIFY_SUBMIT_INFO_sd");
    private static final List<String> plistNewstatisticsFeatureNames = Arrays.asList("hh_new_selfdata_catch_dr",
            "hh_ma_plist_new_dr",
            "hh_ma_plist_textrank_output_dr",
            "app_label_sd");
    private static final List<String> plistOldstatisticsFeatureNames = Arrays.asList("asyncAppListService_sd",
            "hh_plist_authorize_dr",
            "hh_ma_plist_output_tmp_dr",
            "hh_ma_plist_stat_rt_output_dr",
            "hh_ascore_all_202107_rmpaic_dr",
            "hh_unclearedmob38_v2_dr",
            "hh_unclearedmob9p_v2_dr",
            "hh_plist_authorize_dr",
            "allPlist_sd",
            "hh_bscorebyuser_mob3pAU_dr",
            "hh_bscorebyuser_mob02_neverovd_dr",
            "hh_bscorebyuser_inactive_dr",
            "hh_ma_plist_output_tmp_dr",
            "hh_dzbyloan_v3_dr",
            "hh_dzbyuser_v1_dr",
            "hh_ma_plist_fe_output_dr",
            "hh_cleard_v3_dr",
            "hh_ma_plist_stat_bch_output_dr",
            "allPlist_v2_sd",
            "hh_ascore_m3d10_score_202101_dr",
            "hh_dq_dtadjust_dr",
            "hh_FScore_FPD_V3_dr",
            "hh_ascore_all_202107_new_dr");
    private static final List<String> smsNewstatisticsFeatureNames = Arrays.asList("hh_new_selfdata_catch_dr",
            "hh_ma_imessage_newscource_dr",
            "firstloan_sms_sd");
    private static final List<String> smsOldstatisticsFeatureNames = Arrays.asList("hh_sms_test_dr",
            "hh_ma_sms_v2_dr",
            "async_smsReport_sd",
            "rt_test_sms_mzw",
            "smsReport_sd",
            "dz_smsReport_sd",
            "dz_sms_model_feats_sd",
            "async_smsReport_separation_sd");
    private static final List<String> phoneCallNewstatisticsFeatureNames = Arrays.asList("hh_new_selfdata_catch_dr",
            "hh_new_selfdata_catch_dr",
            "phone_call_records_sd");
    private static final List<String> phoneCallOldstatisticsFeatureNames = Arrays.asList("dz_phone_callrecord_sd",
            "async_phonecallrecord_sd",
            "hh_dq_calladb_lend_a_dr",
            "hh_dq_calladb_dr",
            "hh_callnewcsvar_dr",
            "hh_clientadjust_model_v1_dr");
    private static final List<String> phoneBookNewstatisticsFeatureNames = Arrays.asList("hh_new_selfdata_catch_dr",
            "hh_adbvar_old_dr",
            "hh_phoneBookOverdueCount_sd");
    private static final List<String> phoneBookOldstatisticsFeatureNames = Arrays.asList("async_phonebook_sd",
            "dz_phonebook_sd",
            "hh_dq_calladb_lend_a_dr",
            "dz_nl_phonebook_sd",
            "hh_dq_calladb_yace_dr",
            "hh_dq_calladb_dr",
            "hh_phone_book_fraud_dr",
            "hh_phone_book_area_sd",
            "phoneBookDownSideService_sd",
            "hh_dz_Y054_rule_dr",
            "hh_phoneBookContractCountService_dr",
            "hh_phonebook_strategy_score_v1_dr",
            "phoneBookContractCountService_sd",
            "phoneBookLabelService_sd");
    private static final List<String> calendarNewstatisticsFeatureNames = Arrays.asList("hh_new_selfdata_catch_dr");
    private static final List<String> calendarOldstatisticsFeatureNames = Arrays.asList("async_calendarinfo_sd");
    private static final List<String> phoneInfoNewstatisticsFeatureNames = Arrays.asList("hh_base_info_dr",
            "hh_new_selfdata_catch_dr",
            "hh_dz_retrial_activity_score_dr",
            "hh_last_phone_info_sd");
    private static final List<String> phoneInfoOldstatisticsFeatureNames = Arrays.asList("verifyBlack_sd");

    private static final List<String> featureCostTop20 = Arrays.asList("nifa_service_sd",
            "hh_ma_plist_stat_rt_output_dr",
            "hh_bscorebyuser_mob02_neverovd_dr",
            "allPlist_sd",
            "mysqlDb_sd",
            "hh_behaviour_time_dr",
            "dz_phonebook_rulescore_sd",
            "hh_rule_score_new_cal_dr",
            "dz_rule_score_v3_dr",
            "hh_dz_applyrepay_data_dr",
            "hh_ma_tradev5_active_update_dr",
            "async_smsReport_sd",
            "hh_tradev4_byuser_single_dr",
            "hh_ma_tradev5_hisoddays1t10_dr",
            "hh_sms_test_dr",
            "hh_tradev4_byloan_single_dr",
            "hh_tradev5_mob03_dr",
            "hh_ma_plist_new_dr",
            "hh_dq_calladb_dr",
            "hh_bscorebyuser_mob3pAU_dr");


    public static void main(String[] args) {
        CsvReader reader = CsvUtil.getReader();
        //从文件中读取CSV数据
        CsvData data = reader.read(FileUtil.file("C:\\Users\\<USER>\\Downloads\\特征详情.csv"), CharsetUtil.CHARSET_GBK);
        List<CsvRow> rows = data.getRows();
        List<FeatureItemVo> featureItemVos = new ArrayList<>();
        int i = 0;
        for (CsvRow row : rows) {
            if(0 == i){
                i++;
                continue;
            }
            List<String> rawList = row.getRawList();
            FeatureItemVo featureItemVo = FeatureItemVo.builder().eventCode(rawList.get(0))
                    .featureName(rawList.get(1))
                    .featureItemName(rawList.get(2))
                    .featureItemCalNum(Double.parseDouble(rawList.get(3)))
                    .build();
            featureItemVos.add(featureItemVo);
        }
        // --------------------------------------
        Map<String, List<FeatureItemVo>> featureItemVoByF = featureItemVos.stream()
                .collect(Collectors.groupingBy(FeatureItemVo::getFeatureName));

        //
        Map<String, Tuple> featureItemNum = new HashMap<>();
        featureItemVoByF.keySet().forEach(f -> {
                List<FeatureItemVo> featureItemVos1 = featureItemVoByF.get(f);
                // 每个特征项使用的次数
                Map<String, Double> featureItemInfos = new HashMap<>();
                featureItemVos1.forEach(item -> {
                    Double preCalNum = featureItemInfos.getOrDefault(item.getFeatureItemName(), 0D);
                    featureItemInfos.put(item.getFeatureItemName(), preCalNum + item.getFeatureItemCalNum());
                });
                //
                long useItemCount = featureItemInfos.keySet().stream().filter(item -> featureItemInfos.get(item) > 0).count();
                long unUseItemCount = featureItemInfos.keySet().stream().filter(item -> featureItemInfos.get(item) == 0).count();
                featureItemNum.put(f, new Tuple(useItemCount, useItemCount+unUseItemCount));
            }
        );

        //
        Map<String, List<String>> infos = new HashMap<>();
        infos.put("gpsNew", gpsNewstatisticsFeatureNames);
        infos.put("gpsOld", gpsOldstatisticsFeatureNames);
        infos.put("plistNew", plistNewstatisticsFeatureNames);
        infos.put("plistOld", plistOldstatisticsFeatureNames);
        infos.put("smsNew", smsNewstatisticsFeatureNames);
        infos.put("smsOld", smsOldstatisticsFeatureNames);
        infos.put("phoneCallNew", phoneCallNewstatisticsFeatureNames);
        infos.put("phoneCallOld", phoneCallOldstatisticsFeatureNames);
        infos.put("phoneBookNew", phoneBookNewstatisticsFeatureNames);
        infos.put("phoneBookOld", phoneBookOldstatisticsFeatureNames);
        infos.put("calendarNew", calendarNewstatisticsFeatureNames);
        infos.put("calendarOld", calendarOldstatisticsFeatureNames);
        infos.put("phoneInfoNew", phoneInfoNewstatisticsFeatureNames);
        infos.put("phoneInfoOld", phoneInfoOldstatisticsFeatureNames);

        printStatistics(infos, featureItemNum);

        Map<String, List<String>> featureCosts = new HashMap<>();
        AtomicInteger j = new AtomicInteger();
        featureCostTop20.forEach(f -> {
            featureCosts.put(String.valueOf(j), Collections.singletonList(f));
            j.getAndIncrement();
        });

        printStatistics(featureCosts, featureItemNum);

    }

    /**
     * 统计
     * @param infos
     */
    private static void printStatistics(Map<String, List<String>> infos, Map<String, Tuple> featureItemNum){
        Set<Map.Entry<String, List<String>>> entries = infos.entrySet();
        for(Map.Entry<String, List<String>> featureNames :entries){
            String key = featureNames.getKey();
            List<String> value = featureNames.getValue();
            AtomicLong numerator = new AtomicLong();
            AtomicLong denominator = new AtomicLong();
            featureItemNum.keySet().forEach(k -> {
                boolean exsit = value.stream().anyMatch(k::contains);
                if(exsit){
                    System.out.printf("%s %s (%s/%s)%n", key, k, featureItemNum.get(k).get(0), featureItemNum.get(k).get(1));
                    numerator.addAndGet((Long)(featureItemNum.get(k).get(0)));
                    denominator.addAndGet((Long)(featureItemNum.get(k).get(1)));
                }
            });
            System.out.printf("%s (%s/%s)%n", key, numerator, denominator);
        }
    }
}
