package com.example.tools;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.example.common.utils.FileUtil;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class StaticsInfoFeatureItem {

    public static void main(String[] args) throws IOException {
        String dataStr = FileUtil.toOneLine("data.json");
        JSONObject dataVo = JSON.parseObject(dataStr);
        List<JSONObject> allFeatures = new ArrayList<JSONObject>();
        for(String rootKey : dataVo.keySet()){
            try{
                allFeatures.add(dataVo.getJSONObject(rootKey));
            }catch (Exception e){
                System.out.println(rootKey);
            }
        }
        long sumAll = 0;
        for(JSONObject item: allFeatures){
            sumAll += item.keySet().size();
        }
        System.out.println(sumAll);
    }
}
