package com.example.tools.avro;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.example.common.utils.FileUtil;
import com.example.tools.FlatMapUtil;
import com.example.tools.SnappyUtil;
import tech.allegro.schema.json2avro.converter.JsonAvroConverter;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

public class AvroToXml {
    /**
     * 1. 读取xml文件
     * 2. xml转json
     * 3. json转avro schema
     * 4. avro schema转binary avro
     * @param args
     */
    public static void main(String[] args) throws IOException {
        avroSchemaToBAvro();
    }

    private static void avroSchemaToBAvro(){
        try {
            final String xmlStr = FileUtil.toOneLine("mysqldb_sd1.xml");
            JSONObject jsonObject = JSONUtil.parseFromXml(xmlStr);
            String jsonStr = JSON.toJSONString(jsonObject);
            BufferedWriter out = new BufferedWriter(new FileWriter("src/test/resources/preloan_old.json"));
            out.write(jsonStr);
            out.close();

            final String avroSchema = FileUtil.toOneLine("avro.schema");

            String avroBinSnappy = FileUtil.readToString(new File("src/test/resources/avro.bin"));
            String avroBin = SnappyUtil.uncompress(avroBinSnappy);

            JsonAvroConverter converter = new JsonAvroConverter();

            // conversion from binary Avro to JSON
            byte[] binaryJson = converter.convertToJson(avroBin.getBytes(StandardCharsets.ISO_8859_1), avroSchema);
            String s = new String(binaryJson);

            System.out.println(String.format("%s %s %s", jsonStr.length(), s.length(), jsonStr.equals(s)));
            BufferedWriter outNew = new BufferedWriter(new FileWriter("src/test/resources/preloan_new.json"));
            outNew.write(s);
            outNew.close();

            System.out.println(FlatMapUtil.differenceTwoJson(jsonStr, s));
        } catch (IOException e) {
            //
            System.out.println(e);
        }
    }


}
