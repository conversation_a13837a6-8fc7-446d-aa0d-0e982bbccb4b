package com.example.tools.avro;

/**
 */
public class TestHelper {
    public static String getJson(){
        return "{\n" +
                "\t\"deviceDna\": {\n" +
                "\n" +
                "\t\t\"OS\": \"windows\",\n" +
                "\t\t\"AppType\": \"sgmarghade\",\n" +
                "\t\t\"installID\": \"GHRT345GHYT\",\n" +
                "\t\t\"signals\": {\n" +
                "\t\t\t\"DeviceId\": \"Gm9mCkLOtpIsPRbvs2CDZedt4Bw=3D\",\n" +
                "\t\t\t\"OriginalMobileOperatorName\": \"000-IN\",\n" +
                "\t\t\t\"appVersion\": \"9.1.0\",\n" +
                "\t\t\t\"DeviceStatus\": {\n" +
                "\t\t\t\t\"DeviceManufacturer\": \"Microsoft\",\n" +
                "\t\t\t\t\"DeviceName\": \"RM-1067_1005\",\n" +
                "\t\t\t\t\"DeviceFirmwareVersion\": \"02177.00000.15184.36002\",\n" +
                "\t\t\t\t\"DeviceHardwareVersion\": \"*******\",\n" +
                "\t\t\t\t\"DeviceTotalMemory\": \"966664192\",\n" +
                "\t\t\t\t\"PowerSource\": \"External\"\n" +
                "\t\t\t},\n" +
                "\t\t\t\"OSversion\": \"8.1\",\n" +
                "\t\t\t\"ANID2\": \"A=3DE32ACBXXXXXXXXX3B337B68CFFFFFFFF&E=3Dd59&W=3D1\",\n" +
                "\t\t\t\"HostNameDetails\": [{\n" +
                "\t\t\t\t\"CanonicalName\": \"***********\",\n" +
                "\t\t\t\t\"DisplayName\": \"***********\",\n" +
                "\t\t\t\t\"IanaInterfaceType\": \"71\",\n" +
                "\t\t\t\t\"NetworkAdapterId\": \"00f61850-1850-00f6-5018-f6007854f600\",\n" +
                "\t\t\t\t\"NetworkId\": \"\",\n" +
                "\t\t\t\t\"RawName\": \"***********\",\n" +
                "\t\t\t\t\"Type\": \"Ipv4\"\n" +
                "\t\t\t}, {\n" +
                "\t\t\t\t\"CanonicalName\": \"***********\",\n" +
                "\t\t\t\t\"DisplayName\": \"***********\",\n" +
                "\t\t\t\t\"IanaInterfaceType\": \"71\",\n" +
                "\t\t\t\t\"NetworkAdapterId\": \"00f61850-1850-00f6-5018-f6007854f600\",\n" +
                "\t\t\t\t\"NetworkId\": \"\",\n" +
                "\t\t\t\t\"RawName\": \"***********\",\n" +
                "\t\t\t\t\"Type\": \"Ipv4\"\n" +
                "\t\t\t}],\n" +
                "\t\t\t\"InternetConnectionProfile\": {\n" +
                "\t\t\t\t\"ProfileName\": \"belkin.36fe\",\n" +
                "\t\t\t\t\"IanaInterfaceType\": \"71\",\n" +
                "\t\t\t\t\"NetworkAdapterId\": \"89c8990e-085a-40be-bfd6-9b5e4277f261\",\n" +
                "\t\t\t\t\"NetworkId\": \"6aa40415-0002-0000-0000-000000000000\",\n" +
                "\t\t\t\t\"NetworkAuthenticationType\": \"RsnaPsk\",\n" +
                "\t\t\t\t\"NetworkEncryptionType\": \"Ccmp\"\n" +
                "\t\t\t},\n" +
                "\t\t\t\"ConnectonProfiles\": [{\n" +
                "\t\t\t\t\t\"ProfileName\": \"belkin.36fe\",\n" +
                "\t\t\t\t\t\"IanaInterfaceType\": \"71\",\n" +
                "\t\t\t\t\t\"NetworkAdapterId\": \"89c8990e-085a-40be-bfd6-9b5e4277f261\",\n" +
                "\t\t\t\t\t\"NetworkId\": \"6aa40415-0002-0000-0000-000000000000\",\n" +
                "\t\t\t\t\t\"NetworkAuthenticationType\": \"RsnaPsk\",\n" +
                "\t\t\t\t\t\"NetworkEncryptionType\": \"Ccmp\"\n" +
                "\t\t\t\t},\n" +
                "\n" +
                "\t\t\t\t{\n" +
                "\t\t\t\t\t\"ProfileName\": \"belkin.36fe\",\n" +
                "\t\t\t\t\t\"IanaInterfaceType\": \"71\",\n" +
                "\t\t\t\t\t\"NetworkAdapterId\": \"89c8990e-085a-40be-bfd6-9b5e4277f261\",\n" +
                "\t\t\t\t\t\"NetworkId\": \"6aa40415-0002-0000-0000-000000000000\",\n" +
                "\t\t\t\t\t\"NetworkAuthenticationType\": \"RsnaPsk\",\n" +
                "\t\t\t\t\t\"NetworkEncryptionType\": \"Ccmp\"\n" +
                "\t\t\t\t}\n" +
                "\t\t\t],\n" +
                "\t\t\t\"LanIdentifiers\": [{\n" +
                "\t\t\t\t\"InfrastructureId\": \"Windows.Networking.Connectivity.LanIdentifierData\",\n" +
                "\t\t\t\t\"NetworkAdapterId\": \"89c8990e-085a-40be-bfd6-9b5e4277f261\",\n" +
                "\t\t\t\t\"PortId\": \"\"\n" +
                "\t\t\t}],\n" +
                "\t\t\t\"SSID\": \"belkin.36fe\",\n" +
                "\t\t\t\"CellularMobileOperator\": \"cellmobileoperator\",\n" +
                "\t\t\t\"NetworkInferfaceList\": [{\n" +
                "\t\t\t\t\"InterfaceName\": \"belkin.36fe\",\n" +
                "\t\t\t\t\"InterfaceState\": \"Connected\",\n" +
                "\t\t\t\t\"InterfaceType\": \"Wireless80211\",\n" +
                "\t\t\t\t\"InterfaceSubtype\": \"WiFi\",\n" +
                "\t\t\t\t\"Description\": \"belkin.36fe\",\n" +
                "\t\t\t\t\"Characteristics\": \"None\",\n" +
                "\t\t\t\t\"Bandwidth\": \"300000\"\n" +
                "\t\t\t}, {\n" +
                "\t\t\t\t\"InterfaceName\": \"belkin.36fe\",\n" +
                "\t\t\t\t\"InterfaceState\": \"Connected\",\n" +
                "\t\t\t\t\"InterfaceType\": \"Wireless80211\",\n" +
                "\t\t\t\t\"InterfaceSubtype\": \"WiFi\",\n" +
                "\t\t\t\t\"Description\": \"belkin.36fe\",\n" +
                "\t\t\t\t\"Characteristics\": \"None\",\n" +
                "\t\t\t\t\"Bandwidth\": \"300000\"\n" +
                "\t\t\t}]\n" +
                "\n" +
                "\t\t}\n" +
                "\t}\n" +
                "}";
    }

    public static String getJsonWithNullField() {
        return "{\"fieldThatsNull\":null}";
    }

    public static String getJsonWithBooleanField() {
        return "{\"fieldThatsBoolean\":false}";
    }

    public static String getJsonWithEmptyArray() {
        return "{\"emptyArray\":[]}";
    }
}
