package com.example.tools.avro;

import org.apache.avro.Schema;
import org.apache.avro.generic.GenericData;
import org.kitesdk.data.spi.JsonUtil;
import tech.allegro.schema.json2avro.converter.AvroConversionException;
import tech.allegro.schema.json2avro.converter.JsonAvroConverter;

import java.nio.charset.StandardCharsets;

/**
 *  JSON -> Avro schema
 */
public class AvroSchema {
    public static void main(String[] args) {
//        String json = "{\n" +
//                "    \"id\": 1,\n" +
//                "    \"name\": \"A green door\",\n" +
//                "    \"price\": 12.50,\n" +
//                "    \"tags\": [\"home\", \"green\"]\n" +
//                "}\n"
//                ;
//        String avroSchema = JsonUtil.inferSchema(JsonUtil.parse(json), "myschema").toString();
//        System.out.println(avroSchema);

        // json to avro
        jsonToAvro();
    }


    public static void jsonToAvro(){
        // Avro schema with one string field: username
        String schema =
                "{" +
                        "   \"type\" : \"record\"," +
                        "   \"name\" : \"Acme\"," +
                        "   \"fields\" : [{ \"name\" : \"username\", \"type\" : \"string\" },{ \"name\" : \"sex\", \"type\" : [\"string\",\"long\"], \"default\" : \"-999\" }]" +
                        "}";

        String json = "{ \"username\": \"mike\", \"sex\": 1}";

        JsonAvroConverter converter = new JsonAvroConverter();


        // conversion to binary Avro
        byte[] avro = converter.convertToAvro(json.getBytes(), schema);

        // conversion to GenericData.Record
        GenericData.Record record = converter.convertToGenericDataRecord(json.getBytes(), new Schema.Parser().parse(schema));

        // exception handling
        String invalidJson = "{ \"username\": \"mike\"}";

        byte[] avro1 = converter.convertToAvro(invalidJson.getBytes(StandardCharsets.ISO_8859_1), schema);

        // conversion from binary Avro to JSON
        byte[] binaryJson = converter.convertToJson(avro1, schema);

        System.out.println(new String(binaryJson, StandardCharsets.ISO_8859_1));
    }
}
