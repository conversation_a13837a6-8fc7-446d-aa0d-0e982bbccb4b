package com.example.tools.avro;

import cn.hutool.core.date.StopWatch;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.example.common.utils.FileUtil;
import com.example.tools.SnappyUtil;
import org.kitesdk.data.spi.JsonUtil;
import tech.allegro.schema.json2avro.converter.JsonAvroConverter;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

public class XmlToAvro {
    /**
     * 1. 读取xml文件
     * 2. xml转json
     * 3. json转avro schema
     * 4. avro schema转binary avro
     * @param args
     */
    public static void main(String[] args) throws IOException {
        xmlToAvroSchema();
        avroSchemaToBAvro();
    }


    private static void xmlToAvroSchema(){
        try {
            final String xmlStr = FileUtil.toOneLine("mysqldb_sd1.xml");
            JSONObject jsonObject = JSONUtil.parseFromXml(xmlStr);
            String jsonStr = JSON.toJSONString(jsonObject);
            String avroSchema = JsonUtil.inferSchema(JsonUtil.parse(jsonStr), "myschema").toString();

            BufferedWriter out = new BufferedWriter(new FileWriter("src/test/resources/avro.schema"));
            out.write(avroSchema);
            out.close();
        } catch (IOException e) {
            //
            System.out.println(e);
        }
    }

    private static void avroSchemaToBAvro(){
        try {
            final String xmlStr = FileUtil.toOneLine("mysqldb_sd1.xml");
            JSONObject jsonObject = JSONUtil.parseFromXml(xmlStr);
            String jsonStr = JSON.toJSONString(jsonObject);

            final String avroSchema = FileUtil.toOneLine("avro.schema");
            JsonAvroConverter converter = new JsonAvroConverter();

            StopWatch sw = new StopWatch();
            sw.start("任务1");
            // conversion to binary Avro
            byte[] avro = converter.convertToAvro(jsonStr.getBytes(), avroSchema);
            sw.stop();
            String avroStr = new String(avro, StandardCharsets.ISO_8859_1);

            // 571090 347351
            System.out.println(String.format("%s %s %s",
                    xmlStr.getBytes().length, jsonStr.getBytes().length, avroStr.getBytes(StandardCharsets.ISO_8859_1).length));

            // 压缩
            sw.start("任务2");
            String avroSnappy = SnappyUtil.compress(avroStr);
            System.out.println(String.format("压缩后大小: %s", avroSnappy.getBytes(StandardCharsets.ISO_8859_1).length));
            sw.stop();

            System.out.println(sw.prettyPrint());

            BufferedWriter out = new BufferedWriter(new FileWriter("src/test/resources/avro.bin"));
            out.write(avroSnappy);
            out.close();
        } catch (IOException e) {
            //
            System.out.println(e);
        }
    }
}
