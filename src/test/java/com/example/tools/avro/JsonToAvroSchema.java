package com.example.tools.avro;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.example.common.utils.FileUtil;
import com.example.tools.AvroConverter;
import com.example.tools.FlatMapUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import tech.allegro.schema.json2avro.converter.JsonAvroConverter;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

public class JsonToAvroSchema {

    public static void main(String[] args) throws IOException {

        final String xmlStr = FileUtil.toOneLine("mysqldb_sd1.xml");
        JSONObject jsonObject = JSONUtil.parseFromXml(xmlStr);
        String jsonStr = JSON.toJSONString(jsonObject);

        BufferedWriter outOld = new BufferedWriter(new FileWriter("src/test/resources/mysqldb_sd_preloan_old.json"));
        outOld.write(jsonStr);
        outOld.close();

        final ObjectMapper mapper = new ObjectMapper();
        AvroConverter avroConverter = new AvroConverter(mapper);
        String avroSchema = avroConverter.convert(jsonStr);

        BufferedWriter out = new BufferedWriter(new FileWriter("src/test/resources/mysqldb_sd_avro_new.schema"));
        out.write(avroSchema);
        out.close();

        // conversion to binary Avro
        JsonAvroConverter converter = new JsonAvroConverter();
        byte[] avro = converter.convertToAvro(jsonStr.getBytes(), avroSchema);
        String avroStr = new String(avro, StandardCharsets.ISO_8859_1);

        BufferedWriter outAvro = new BufferedWriter(new FileWriter("src/test/resources/mysqldb_sd_avro_new.bin"));
        outAvro.write(avroStr);
        outAvro.close();

        // conversion from binary Avro to JSON
        byte[] binaryJson = converter.convertToJson(avroStr.getBytes(StandardCharsets.ISO_8859_1), avroSchema);
        String s = new String(binaryJson);

        BufferedWriter outJson = new BufferedWriter(new FileWriter("src/test/resources/mysqldb_sd_preloan_new_2.json"));
        outJson.write(s);
        outJson.close();


        System.out.println(FlatMapUtil.differenceTwoJson(jsonStr, s));
    }
}
