package com.example.tools.test1;

import com.example.tools.DelayQueueTest;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.redisson.Redisson;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RDelayedQueue;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.Serializable;
import java.nio.file.Path;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.IntStream;

public class Test2Test {

    private final static Logger logger = LoggerFactory.getLogger(DelayQueueTest.class);

    @Test
    public void testMain() {
        // Setup
        // Run the test
        Test2.main(new String[]{"args"});

        // Verify the results
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    static class Message implements Serializable {
        private static final long serialVersionUID = 6151282779474571204L;

        private String id;

        private String data;
    }

    @Test
    public void testRedisDelayedQueue() {
        int maxRoundCount = 10;
        int roundMessageCount = 1000;
        final CountDownLatch downLatch = new CountDownLatch(maxRoundCount);
        Config config = new Config();
        config.setNettyThreads(200);
        config.useSingleServer()
                .setTimeout(3000)
                .setKeepAlive(true)
                .setDatabase(1)
                .setPingConnectionInterval(3000)
                .setTcpNoDelay(true)
                .setRetryInterval(3000)
                .setRetryAttempts(4)
                .setIdleConnectionTimeout(5 * 60000)
                .setConnectionPoolSize(30)
                .setAddress("redis://127.0.0.1:6379");
        RedissonClient redissonClient = Redisson.create(config);
        Map<Integer, RBlockingQueue<Message>> rBlockingQueueHashMap = Maps.newHashMap();
        IntStream.range(0, maxRoundCount).forEach(roundId -> {
            RBlockingQueue<Message> destinationQueue = redissonClient.getBlockingQueue("delay_shard_queue_" + roundId);
            rBlockingQueueHashMap.put(roundId, destinationQueue);
        });
        IntStream.range(0, maxRoundCount).forEach(roundId -> {
            RBlockingQueue<Message> blockingQueue = rBlockingQueueHashMap.get(roundId);
            try {
                new Thread(() -> {
                    long maxDiff = 0L;
                    final AtomicInteger messageCount = new AtomicInteger(0);
                    while (messageCount.get() < roundMessageCount) {
                        try {
                            Message ms = blockingQueue.poll(1, TimeUnit.MILLISECONDS);
                            if (ms == null) {
                                continue;
                            }
                            Long actualDelayMs = (System.currentTimeMillis() - Long.parseLong(ms.getId()));
                            Long expectDelayMs = Integer.parseInt(ms.getData()) * 1000L;
                            long diffDelayMs = Math.abs(actualDelayMs - expectDelayMs);
                            maxDiff = Math.max(maxDiff, diffDelayMs);
                            logger.info("expectDelayMs: {}, actualDelayMs: {}, diffDelayMs: {}", expectDelayMs, actualDelayMs, diffDelayMs);
                            messageCount.incrementAndGet();
                        } catch (Throwable t) {
                            t.printStackTrace();
                        }
                    }
                    logger.info("## round: {}, message count: {}, maxDiff: {}", roundId, messageCount.get(), maxDiff);
                    downLatch.countDown();
                }).start();
            } catch (Throwable t) {
                t.printStackTrace();
            }
            new Thread(() -> IntStream.range(0, roundMessageCount).mapToLong(i -> System.currentTimeMillis()).forEach(currentTime -> {
                int n2 = RandomUtils.nextInt(1, 5);
                Message message = new Message(currentTime + "", n2 + "");
                RDelayedQueue<Message> delayedQueue = redissonClient.getDelayedQueue(blockingQueue);
                delayedQueue.offer(message, n2, TimeUnit.SECONDS);
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            })).start();
        });
        try {
            downLatch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        redissonClient.shutdown(3000, 6000, TimeUnit.MILLISECONDS);
    }
}
