package com.example.tools;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.util.Assert;

import java.util.Random;
import java.util.TimerTask;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@SpringBootTest
class ToolsJavaApplicationTests {

    @Test
    void contextLoads() {
    }

    public static void main(String[] args) {
        ScheduledExecutorService pool = Executors.newSingleThreadScheduledExecutor();
        Random random = new Random();

        pool.scheduleWithFixedDelay(() -> {
            int i = random.nextInt(100);
            System.out.println(i);
            if (i % 5 == 0) {
                throw new RuntimeException("Exception triggered! HAHA");
            }
        }, 1, 3, TimeUnit.SECONDS);
    }

    @Test
    public void testTimer() {
        final AtomicInteger cTries = new AtomicInteger(0);
        final AtomicInteger cSuccesses = new AtomicInteger(0);

        TimerTask task = new TimerTask() {
            @Override
            public void run()
            {
                cTries.incrementAndGet();
                if (true) {
                    throw new RuntimeException();
                }
                cSuccesses.incrementAndGet();
            }
        };

    /*
    Timer t = new Timer();
    t.scheduleAtFixedRate(task, 0, 500);
     */
        ScheduledExecutorService exe = Executors.newSingleThreadScheduledExecutor();
        exe.scheduleAtFixedRate(task, 0, 500, TimeUnit.MILLISECONDS);
        synchronized (this) {
            try {
                wait(3000);
            } catch (InterruptedException e) {
                e.printStackTrace();  //To change body of catch statement use File | Settings | File Templates.
            }
        }
        exe.shutdown();
    /*
    t.purge();
     */
        Assert.isTrue(cSuccesses.get() == 0, "");
        Assert.isTrue(cTries.get() > 1, String.format("%d is not greater than 1. :(", cTries.get()));
    }
}
