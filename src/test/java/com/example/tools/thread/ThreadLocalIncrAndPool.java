package com.example.tools.thread;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class ThreadLocalIncrAndPool {

    private static ThreadLocal<Map<String,Integer[]>> variableLocal = null;// ThreadLocal.withInitial(() -> 0);

    public static int getOne(String key) {
        return variableLocal.get().get(key)[0];
    }
    public static int getTwo(String key) {
        return variableLocal.get().get(key)[1];
    }

    public static void init(){
        variableLocal.set(new HashMap<>());
    }

    public static void remove() {
        variableLocal.remove();
    }

    public static Integer[] get(String key){
        return variableLocal.get().get(key);
    }

    public static void incrementOne(String key) {
        ++variableLocal.get().get(key)[0];
    }

    public static void incrementTwo(String key) {
        ++variableLocal.get().get(key)[1];
    }

    public static void main(String[] args) {
        ExecutorService executorService = new ThreadPoolExecutor(2,2,0, TimeUnit.SECONDS,new LinkedBlockingDeque<>(12));

        for(int i=0;i<5;i++){
            executorService.execute(()->{
                long threadId = Thread.currentThread().getId();
                String threadName =  Thread.currentThread().getName();
                init();
                if(variableLocal.get().containsKey("key")){
                    variableLocal.get().put("key", new Integer[]{0,0});
                }
                Integer[] before = get("key");
//                incrementOne();
//                int after = get();
//                System.out.println("threadid:" + threadId +"  threadName:"+threadName +"  before:" + before + "  after:" + after);
                /* threadlocal与线程池使用的问题了，因为threadlocal维护是 Map<Thread,T>这个结构。
                 * 而线程池是对线程进行复用的，如果没有及时的清理，那么之前对该线程的使用，就会影响到后面的线程了，造成数据不准确。
                 * */
                remove();//增加remove,解决内存泄露
            });
        }
        executorService.shutdown();
    }
}

