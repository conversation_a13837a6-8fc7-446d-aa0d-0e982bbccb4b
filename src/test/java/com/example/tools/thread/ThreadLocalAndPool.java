package com.example.tools.thread;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class ThreadLocalAndPool {

    private static ThreadLocal<Integer> variableLocal = ThreadLocal.withInitial(() -> 0);

    public static int get() {
        return variableLocal.get();
    }

    public static void remove() {
        variableLocal.remove();
    }

    public static void increment() {
        variableLocal.set(variableLocal.get() + 1);
    }

    public static void main(String[] args) {
        ExecutorService executorService = new ThreadPoolExecutor(2,2,0, TimeUnit.SECONDS,new LinkedBlockingDeque<>(12));

        for(int i=0;i<5;i++){
            executorService.execute(()->{
                long threadId = Thread.currentThread().getId();
                String threadName =  Thread.currentThread().getName();
                int before = get();
                increment();
                int after = get();
                System.out.println("threadid:" + threadId +"  threadName:"+threadName +"  before:" + before + "  after:" + after);
                /* threadlocal与线程池使用的问题了，因为threadlocal维护是 Map<Thread,T>这个结构。
                 * 而线程池是对线程进行复用的，如果没有及时的清理，那么之前对该线程的使用，就会影响到后面的线程了，造成数据不准确。
                 * */
                remove();//增加remove,解决内存泄露
            });
        }
        executorService.shutdown();
    }
}

