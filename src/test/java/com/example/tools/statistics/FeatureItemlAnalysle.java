package com.example.tools.statistics;

import cn.hutool.core.text.csv.CsvData;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvRow;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.util.CharsetUtil;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

public class FeatureItemlAnalysle {

    public static void main(String[] args) throws DocumentException, IOException, ParserConfigurationException, SAXException {

        CsvReader reader = CsvUtil.getReader();
        //从文件中读取CSV数据  query-hive-765058  query-hive-764884
        CsvData data = reader.read(cn.hutool.core.io.FileUtil.file("C:\\Users\\<USER>\\Downloads\\query-hive-765297.csv"), CharsetUtil.CHARSET_GBK);
        List<CsvRow> rows = data.getRows();

        for (CsvRow row : rows) {
            List<String> rawList = row.getRawList();
            String type = rawList.get(0);
            String xmlStr = rawList.get(2);
            printStatistics(type, xmlStr);
        }

    }


    private static void printStatistics(String type, String xmlStr) throws DocumentException {

        Document doc = DocumentHelper.parseText(xmlStr);
        Map<String, Integer> xmlKeys = new HashMap<>();
        //
        Element rootElement = doc.getRootElement();
        Element standard = rootElement.element("standard");
        List<Element> elements = standard.elements();
        elements.forEach(e -> {
            Set<String> featureItemNames = new HashSet<>();
            e.elements().forEach(i -> featureItemNames.add(i.getName()));
            xmlKeys.put(e.getName(), featureItemNames.size());
        });
        //
//        xmlKeys.keySet()
//                .stream()
//                .map(k -> new Tuple(k, xmlKeys.get(k)))
//                .sorted(Comparator.comparing(v -> v.get(1)))
//                .forEach(System.out::println);

        AtomicReference<Integer> itemSize = new AtomicReference<>(0);
        xmlKeys.keySet().forEach(k -> {
            Integer size = xmlKeys.get(k);
            itemSize.set(itemSize.get() + size);
        });

        System.out.printf("%s %s%n", type, itemSize.get());
    }
}
