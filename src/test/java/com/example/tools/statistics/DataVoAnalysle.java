package com.example.tools.statistics;


import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.lang.Tuple;
import com.alibaba.fastjson.JSONObject;
import org.junit.jupiter.api.Test;


import java.util.Comparator;
import java.util.Optional;

public class DataVoAnalysle {

    @Test
    public void testOrderKVSize() {
        final String dataStr = ResourceUtil.readUtf8Str("fp_02.json");
        JSONObject dataVo = JSONObject.parseObject(dataStr);
        JSONObject thirdPartyData = dataVo.getJSONObject("thirdPartyData");
        Optional.ofNullable(thirdPartyData)
                .orElse(new JSONObject())
                .keySet()
                .forEach(k -> dataVo.put("thirdPartyData_" + k, thirdPartyData.getString(k)));
        dataVo.remove("thirdPartyData");

        dataVo.keySet()
                .stream()
                .map(k -> new Tuple(k, dataVo.getString(k).length()))
                .sorted(Comparator.comparing((Tuple v) -> v.get(1)).reversed())
                .forEach(System.out::println);
    }
}
