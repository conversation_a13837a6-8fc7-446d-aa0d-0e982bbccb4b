package com.example.tools.statistics;


import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.lang.Tuple;
import com.alibaba.fastjson.JSONObject;
import org.junit.jupiter.api.Test;

import java.util.Comparator;
import java.util.Optional;

public class FeaturesAnalysle {

    @Test
    public void testOrderKVSize() {
        final String dataStr = ResourceUtil.readUtf8Str("preFeatures.json");
        JSONObject dataVo = JSONObject.parseObject(dataStr);
        JSONObject standard = dataVo.getJSONObject("feature").getJSONObject("standard");
        Optional.ofNullable(standard)
                .orElse(new JSONObject())
                .keySet()
                .forEach(k -> dataVo.put(k, standard.getString(k)));

        dataVo.remove("feature");

        dataVo.keySet()
                .stream()
                .map(k -> new Tuple(k, dataVo.getString(k).length()))
                .sorted(Comparator.comparing(v -> v.get(1)))
                .forEach(System.out::println);
    }
}
