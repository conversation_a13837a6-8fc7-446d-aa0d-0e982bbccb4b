package com.example.tools.statistics;

import cn.hutool.core.lang.Tuple;
import com.example.common.utils.FileUtil;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import java.io.IOException;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class XmlAnalysle {

    public static void main(String[] args) throws DocumentException, IOException, ParserConfigurationException, SAXException {
        final String xmlStr = FileUtil.toOneLine("td_service_sd.xml");
        Document doc = DocumentHelper.parseText(xmlStr);
        Map<String, Integer> xmlKeys = new HashMap<>();
        //
        Element rootElement = doc.getRootElement();
        rootElement.elements().forEach(e -> {
            if(!"standard".equals(e.getName())){
                xmlKeys.put(e.getName(), e.asXML().trim().length());
            }
        });
        //
        Element standard = rootElement.element("standard");
        List<Element> elements = standard.elements();
        elements.forEach(e -> {
            xmlKeys.put(e.getName(), xmlKeys.getOrDefault(e.getName(), 0) + e.asXML().trim().length());
        });
        //
        xmlKeys.keySet()
                .stream()
                .map(k -> new Tuple(k, xmlKeys.get(k)))
                .sorted(Comparator.comparing(v -> v.get(1)))
                .forEach(System.out::println);
    }

}
