package com.example.tools.gzip;

import com.example.common.utils.FileUtil;
import com.example.tools.GZIPUtils;

import java.io.IOException;

public class GzipTest {

    public static void main(String[] args) throws IOException {
        StringBuilder xmlStr = new StringBuilder(FileUtil.toOneLine("data.json"));
        for(int i=0; i<5; i++){
            xmlStr.append(xmlStr);
        }
        System.out.println("字符串长度："+xmlStr.length()*1.0/1024/1024);
        long startTime = System.currentTimeMillis();
        int times = 2000;
        byte[] compressStr = new byte[0];
        for (int i = 0; i < times; i++) {
            compressStr = GZIPUtils.compress(xmlStr.toString());
        }
        System.out.println((System.currentTimeMillis() - startTime)*1.0/times);
        System.out.println("压缩后: " + compressStr.length*1.0/1024/1024);
        System.out.println("压缩比：："+ (xmlStr.length() - compressStr.length)*1.0/xmlStr.length());

        startTime = System.currentTimeMillis();
        System.out.println("解压后："+GZIPUtils.uncompress(GZIPUtils.compress(xmlStr.toString())).length/1024/1024);
        System.out.println(System.currentTimeMillis() - startTime);
        System.out.println("解压字符串后：："+GZIPUtils.uncompressToString(GZIPUtils.compress(xmlStr.toString())).length());
        //字符串长度：40.57373046875
        //705.7475
        //压缩后: 4.2003068923950195
        //压缩比：：0.8964771825546062
        //解压后：41
        //832
        //解压字符串后：：42544640
        //
        //Process finished with exit code 0
    }
}
