package com.example.tools;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Data
@Accessors(chain = true)
class User{
    private String name;
    private Integer age;
}

public class Sorted {
    public static void main(String[] args) {

        List<User> list=new ArrayList<>();
        list.add(new User().setName("zhang").setAge(13));
        list.add(new User().setName("wang").setAge(14));
        list.add(new User().setName("li").setAge(15));
        list.add(new User().setName("zhao").setAge(16));
        list.add(new User().setName("liu").setAge(12));


        System.out.println(list.stream().sorted(Comparator.comparing(User::getAge))
                .collect(Collectors.toList()));
    }
}
