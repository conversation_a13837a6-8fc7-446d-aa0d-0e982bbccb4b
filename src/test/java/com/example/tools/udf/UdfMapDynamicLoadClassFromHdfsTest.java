package com.example.tools.udf;

import com.example.service.RelationService;
import com.example.tools.UdfClassLoader;
import org.apache.hadoop.hive.ql.exec.UDFArgumentException;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDF;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

public class UdfMapDynamicLoadClassFromHdfsTest extends GenericUDF {

    private static final Logger LOGGER =
            LoggerFactory.getLogger(UdfMapDynamicLoadClassFromHdfsTest.class);

    private static final RelationService RELATION_SERVICE;

    static {
        try {
            UdfClassLoader udfClassLoader =
                    new UdfClassLoader(
                            "viewfs://c9/user_ext/weibo_rd_dip/udf/jar",
                            "udf_map_dynamic_load_class_from_hdfs_test_impl");

            RELATION_SERVICE =
                    (RelationService)
                            udfClassLoader
                                    .loadClass("com.weibo.dip.hubble.mis.udf.example.dynamic.RelationServiceImpl")
                                    .newInstance();
        } catch (Exception e) {
            throw new ExceptionInInitializerError(e);
        }
    }

    @Override
    public ObjectInspector initialize(ObjectInspector[] arguments) throws UDFArgumentException {
        // 判断有没有加载资源

        return null;
    }

    @Override
    public Object evaluate(DeferredObject[] arguments) throws HiveException {
//        String key = (String) converter.convert(arguments[0].get());
        String key = "";

        RELATION_SERVICE.get(key);

        return null;
    }

    @Override
    public String getDisplayString(String[] children) {
        return null;
    }

    @Override
    public void close() throws IOException {

    }
}
