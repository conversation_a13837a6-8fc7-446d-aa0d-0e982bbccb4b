package com.example.tools;

import com.baidubce.services.bos.BosClient;

import java.io.File;

/**
 * http://baidu.netnic.com.cn/doc/BOS/Java-SDK.html#.E4.BD.BF.E7.94.A8.E4.BB.A3.E7.90.86
 * https://github.com/baidubce/bce-sdk-java
 * https://www.jianshu.com/p/1f723cebbae9
 * https://www.cnblogs.com/tianxiaxuange/p/11205729.html
 * https://www.cnblogs.com/tianxiaxuange/p/11205729.html
 *
 * https://vergessenwang.github.io/posts/Java/2020-01-20-JavaFX-SQLite%E5%AE%9E%E7%8E%B0%E7%AE%80%E5%8D%95%E7%B3%BB%E7%BB%9F.html
 * https://edencoding.com/connect-javafx-with-sqlite/
 */
public class Sample {

    private static final String ACCESS_KEY_ID = "your accesskey id";
    private static final String SECRET_ACCESS_KEY = "your secret accesskey";
    private static final String ENDPOINT = "https://bj.bcebos.com";          // 用户自己指定的域名
    private static final String FLIE_PATH = "";
    private static final String BUCKET_NAME = "";

    public static void main(String[] args) {
        BosClient client = BosUtils.getBosClient(ACCESS_KEY_ID,SECRET_ACCESS_KEY, ENDPOINT);
        File file = new File(FLIE_PATH);
        BosUtils.uploadFileToBos(client,file, BUCKET_NAME,"test.jpg");
    }
}
