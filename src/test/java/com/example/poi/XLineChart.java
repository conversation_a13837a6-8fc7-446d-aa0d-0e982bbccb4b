package com.example.poi;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.xddf.usermodel.*;
import org.apache.poi.xddf.usermodel.chart.*;
import org.apache.poi.xssf.usermodel.*;

import java.io.FileOutputStream;
import java.io.IOException;


/**
 * Line chart example.
 */
public final class XLineChart {
    private XLineChart() {}

    public static void main(String[] args) throws IOException {
        try (XSSFWorkbook wb = new XSSFWorkbook()) {
            XSSFSheet sheet = wb.createSheet("linechart");

            // Create data for the chart
            Object[][] sheetData = new Object[][] {
                    new Object[] {"Categories", 1d, 2d, 3d, 4d, 5d, 6d, 7d, 8d, 9d, 10d},
                    new Object[] {"Series 1", 0d, 1d, 2d, 3d, 4d, 5d, 6d, 7d, 8d, 9d},
                    new Object[] {"Series 2", 0.5d, 2.5d, 4.5d, 6.5d, 8.5d, 10.5d, 12.5d, 14.5d, 16.5d, 18.5d}
            };

            // Put data for the chart into the sheet
            Row row;
            Cell cell;
            int rowIndex = 0;
            for (Object[] rowData : sheetData) {
                row = sheet.createRow(rowIndex++);
                int colIndex = 0;
                for (Object cellData : rowData) {
                    cell = row.createCell(colIndex++);
                    if (cellData instanceof String) {
                        cell.setCellValue((String)cellData);
                    } else if (cellData instanceof Double) {
                        cell.setCellValue((Double)cellData);
                    }
                }
            }

            XSSFDrawing drawing = sheet.createDrawingPatriarch();
            XSSFClientAnchor anchor = drawing.createAnchor(0, 0, 0, 0, 0, 5, 10, 15);

            XSSFChart chart = drawing.createChart(anchor);
            XDDFChartLegend legend = chart.getOrAddLegend();
            legend.setPosition(LegendPosition.TOP_RIGHT);

            // Use a category axis for the bottom axis.
            XDDFCategoryAxis bottomAxis = chart.createCategoryAxis(AxisPosition.BOTTOM);
            bottomAxis.setTitle("x"); // https://stackoverflow.com/questions/32010765
            XDDFValueAxis leftAxis = chart.createValueAxis(AxisPosition.LEFT);
            leftAxis.setTitle("f(x)");
            leftAxis.setCrosses(AxisCrosses.AUTO_ZERO);

            XDDFDataSource<Double> xs = XDDFDataSourcesFactory.fromNumericCellRange(sheet, new CellRangeAddress(0, 0, 1, 10));
            XDDFNumericalDataSource<Double> ys1 = XDDFDataSourcesFactory.fromNumericCellRange(sheet, new CellRangeAddress(1, 1, 1, 10));
            XDDFNumericalDataSource<Double> ys2 = XDDFDataSourcesFactory.fromNumericCellRange(sheet, new CellRangeAddress(2, 2, 1, 10));

            XDDFLineChartData data = (XDDFLineChartData) chart.createData(ChartTypes.LINE, bottomAxis, leftAxis);
            XDDFLineChartData.Series series1 = (XDDFLineChartData.Series) data.addSeries(xs, ys1);

            //series1.setTitle("2x", null); // https://stackoverflow.com/questions/21855842
            series1.setTitle("Series 1", new CellReference(sheet.getSheetName(), 1, 0, true, true));

            series1.setSmooth(false); // https://stackoverflow.com/questions/29014848
            series1.setMarkerStyle(MarkerStyle.CIRCLE); // https://stackoverflow.com/questions/39636138
            series1.setMarkerSize((short) 10);
            XDDFLineChartData.Series series2 = (XDDFLineChartData.Series) data.addSeries(xs, ys2);

            //series2.setTitle("3x", null);
            series2.setTitle("Series 2", new CellReference(sheet.getSheetName(), 2, 0, true, true));

            series2.setSmooth(true);
            series2.setMarkerStyle(MarkerStyle.CIRCLE); // https://stackoverflow.com/questions/39636138
            series2.setMarkerSize((short) 10);
            chart.plot(data);

            // if your series have missing values like https://stackoverflow.com/questions/29014848
            // chart.displayBlanksAs(DisplayBlanks.GAP);

            // https://stackoverflow.com/questions/24676460
            solidLineSeries(series1, PresetColor.CHARTREUSE);
            solidLineSeries(series2, PresetColor.TURQUOISE);

            // Write the output to a file
            try (FileOutputStream fileOut = new FileOutputStream("ooxml-line-chart.xlsx")) {
                wb.write(fileOut);
            }
        }
    }

    private static void solidLineSeries(XDDFChartData.Series series, PresetColor color) {
        XDDFSolidFillProperties fill = new XDDFSolidFillProperties(XDDFColor.from(color));
        XDDFLineProperties line = new XDDFLineProperties();
        line.setFillProperties(fill);
        //XDDFChartData.Series series = data.getSeries().get(index);
        XDDFShapeProperties properties = series.getShapeProperties();
        if (properties == null) {
            properties = new XDDFShapeProperties();
        }
        properties.setLineProperties(line);
        series.setShapeProperties(properties);
    }
}