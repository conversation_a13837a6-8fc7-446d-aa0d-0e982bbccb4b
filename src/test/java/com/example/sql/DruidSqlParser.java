package com.example.sql;

import com.alibaba.druid.DbType;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.parser.SQLParserUtils;
import com.alibaba.druid.sql.parser.SQLStatementParser;
import org.apache.hadoop.hive.ql.parse.ASTNode;
import org.apache.hadoop.hive.ql.parse.ParseDriver;
import org.apache.hadoop.hive.ql.parse.ParseException;

public class DruidSqlParser {
    public static void main(String[] args) throws ParseException {
        String sql = "FROM (SELECT p.datekey datekey, p.userid userid, c.clienttype  FROM detail.usersequence_client c JOIN fact.orderpayment p ON p.orderid = c.orderid  JOIN default.user du ON du.userid = p.userid WHERE p.datekey = 20131118 ) base  INSERT OVERWRITE TABLE `test`.`customer_kpi` SELECT base.datekey,   base.clienttype, count(distinct base.userid) buyer_count GROUP BY base.datekey, base.clienttype";

        final SQLStatementParser hive = SQLParserUtils.createSQLStatementParser(sql, DbType.hive);
        final SQLStatement statement = hive.parseStatement();
        System.out.println(statement);

        // as you can see , using this parseDriver will cause an error
        ParseDriver pd = new ParseDriver();
        ASTNode ast = pd.parse(sql);
        System.out.println(ast.dump());
    }
}
