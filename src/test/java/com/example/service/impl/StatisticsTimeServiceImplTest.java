package com.example.service.impl;

import cn.hutool.core.date.DateUtil;
import com.example.common.constant.RiskInfoUtils;
import com.example.common.utils.ExcelUtil;
import com.example.model.EventCodeCostTime;
import com.example.model.EventCodeNodeCostTime;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;
import java.util.stream.Collectors;

@RunWith(SpringRunner.class)
@SpringBootTest
public class StatisticsTimeServiceImplTest {

    @Autowired
    private StatisticsTimeServiceImpl statisticsTimeServiceImplUnderTest;

    /**
     * 耗时填充0 问题
     */
    @Test
    public void testGetNodeCostByEventCode() {
        for(String eventCode : RiskInfoUtils.eventCodes.keySet()){
            long startTime = getTime(eventCode, "startTime");
            long endTime = getTime(eventCode, "endTime");
            long lastStartTime = getTime(eventCode, "lastStartTime");
            long lastEndTime = getTime(eventCode, "lastEndTime");
            System.out.println(
                    "开始时间(本周): "+  DateUtil.formatDateTime(DateUtil.date(startTime))
                    + " 结束时间(本周): " +  DateUtil.formatDateTime(DateUtil.date(endTime))
                    + " 开始时间(上周): "+  DateUtil.formatDateTime(DateUtil.date(lastStartTime))
                    + " 结束时间(上周): " +  DateUtil.formatDateTime(DateUtil.date(lastEndTime))
            );
            System.out.println("【本周】===============================================================================================");
            totalCostTime(eventCode, startTime, endTime);
            System.out.println("【上周】===============================================================================================");
            totalCostTime(eventCode, lastStartTime, lastEndTime);
//            System.out.println("===============================================================================================");
            Map<String, Object[][]> costData = getCostData(eventCode, startTime, endTime);
//            System.out.println("===============================================================================================");
            Map<String, Object[][]> lastWeekCostData = getCostData(eventCode, lastStartTime, lastEndTime);
//            System.out.println("===============================================================================================");
            // 耗时加工
            Map<String, Object[][]> excelDatas = costProcess(costData, lastWeekCostData);
            String excelFileName = "src/test/resources/" + String.join("_" , eventCode, DateUtil.today()) + ".xlsx";
            String[] title = {"节点名称", "上周", "本周"};
            ExcelUtil.exportFeedBack(excelFileName, excelDatas, title);
        }
    }

    private Object[][] excelDataProcess(String eventCode, Map<String, List<EventCodeNodeCostTime>> eventCodeNodeCostTimeMap, String infoKey){
        Map<String, List<String>> eventCodeInfo = RiskInfoUtils.eventCodes.get(eventCode).get(infoKey);
        Object[][] nodeInfos = new Object[eventCodeInfo.size()][2];
        int i = 0;
        for(Map.Entry<String, List<String>> entry : eventCodeInfo.entrySet()){
            String name = entry.getKey();
            double nodeCostValue = entry.getValue().stream().map(v -> {
                if(CollectionUtils.isNotEmpty(eventCodeNodeCostTimeMap.get(v))){
                    return eventCodeNodeCostTimeMap.get(v).stream().filter(item -> StringUtils.isNotEmpty(item.getMeanCostStr())).collect(Collectors.averagingDouble(EventCodeNodeCostTime::getMeanCost));
                }
                return 0D;
            }).collect(Collectors.averagingDouble(value -> value));
//            System.out.printf("节点名称： %s  %.1f%n", name, nodeCostValue/1000);
            nodeInfos[i][0] = name;
            nodeInfos[i][1] = Double.parseDouble(String.format("%.1f", nodeCostValue / 1000));
            i++;
        }
        return nodeInfos;
    }

    private void printInfo(Map<String, List<EventCodeNodeCostTime>> eventCodeNodeCostTimeMap){
        Set<Map.Entry<String, List<EventCodeNodeCostTime>>> entries = eventCodeNodeCostTimeMap.entrySet();
        for(Map.Entry<String, List<EventCodeNodeCostTime>> entry : entries){
            String nodeName = entry.getKey();
            List<EventCodeNodeCostTime> eventCodeNodeCostTimes = entry.getValue();

            Double acceptValue = eventCodeNodeCostTimes.stream().collect(Collectors.averagingDouble(EventCodeNodeCostTime::getMeanCost));
            System.out.printf("节点名称： %s  %.1f%n", nodeName, acceptValue/1000);
        }
    }

    private String titleParse(String key, String eventCode){
        String verifyResult = key.split("_")[0].equals("None")?"":key.split("_")[0];
        String isNewLoan = "";
        if(key.split("_").length > 1){
            isNewLoan = key.split("_")[1].equals("None")?"":key.split("_")[1];
        }
        List<String> titles = new ArrayList<>();
        if(StringUtils.isNotEmpty(verifyResult)){
            titles.add(verifyResult);
        }
        if(StringUtils.isNotEmpty(isNewLoan)){
            titles.add(isNewLoan);
        }
        titles.add(eventCode);
        return String.join("_", titles);
    }


    private Map<String, Object[][]> getCostData(String eventCode, Long startTime, Long endTime){
        final List<EventCodeNodeCostTime> result = statisticsTimeServiceImplUnderTest.getNodeCostByEventCode(eventCode, startTime, endTime);
        Map<String, List<EventCodeNodeCostTime>> eventCodeNodeCostTimeMap = new HashMap<>();
        // 是否通过、是否首复贷 分组分析
        Map<String, List<EventCodeNodeCostTime>> eventCodeNodeCostTimeMapOne = result.stream()
                .collect(Collectors.groupingBy(g -> String.join("_",
                        g.getVerifyResult(),
                        g.getIsNewLoan())));
        // 是否通过分组
        Map<String, List<EventCodeNodeCostTime>> eventCodeNodeCostTimeMapTwo = result.stream()
                .collect(Collectors.groupingBy(g -> String.join("_",
                        g.getVerifyResult())));

        // 是否通过分组
        Map<String, List<EventCodeNodeCostTime>> eventCodeNodeCostTimeMapThree = result.stream()
                .collect(Collectors.groupingBy(g -> String.join("_", "None")));

        eventCodeNodeCostTimeMap.putAll(eventCodeNodeCostTimeMapTwo);
        eventCodeNodeCostTimeMap.putAll(eventCodeNodeCostTimeMapThree);
//        if(eventCode.equals("haoHuanLendAudit") || eventCode.equals("haoHuanLendAuditReloan")){
//            eventCodeNodeCostTimeMap.putAll(eventCodeNodeCostTimeMapOne);
//        }else {
//            eventCodeNodeCostTimeMap.putAll(eventCodeNodeCostTimeMapTwo);
//            eventCodeNodeCostTimeMap.putAll(eventCodeNodeCostTimeMapThree);
//        }

        Map<String, Object[][]> excelDatas = new LinkedHashMap<>();
        for(Map.Entry<String, List<EventCodeNodeCostTime>> entry : eventCodeNodeCostTimeMap.entrySet()){
            List<EventCodeNodeCostTime> eventCodeNodeCostTimes = entry.getValue();
            // entry.getKey() None结尾的过滤掉
            if(entry.getKey().endsWith("None")){
                continue;
            }
            String key = titleParse(entry.getKey(), eventCode);
            if(excelDatas.containsKey(key)){
                continue;
            }
            Map<String, List<EventCodeNodeCostTime>> data = eventCodeNodeCostTimes.stream()
                    .collect(Collectors.groupingBy(EventCodeNodeCostTime::getNode));

            Object[][] nodeInfos = excelDataProcess(eventCode, data, key);

            excelDatas.put(key + "_耗时统计", nodeInfos);
        }
        return excelDatas;
    }


    private Long getTime(String eventCode, String key){
        Long time = null;
        String timeConfig = RiskInfoUtils.eventCodeTimeRanges.get(eventCode).get(key);
        if(StringUtils.isNotEmpty(timeConfig)){
            time =  DateUtil.parse(timeConfig).getTime();
        }
        return time;
    }

    private void totalCostTime(String eventCode, Long startTime, Long endTime){
        final List<EventCodeCostTime> totalResult = statisticsTimeServiceImplUnderTest.getTotalCostByEventCode(eventCode, startTime, endTime);

        Map<String, List<EventCodeCostTime>> collect = totalResult.stream()
                .collect(Collectors.groupingBy(g -> g.getVerifyResult() + "_" + g.getIsNewLoan()));

        for(Map.Entry<String, List<EventCodeCostTime>> entry: collect.entrySet()){
            String key = entry.getKey();
            List<EventCodeCostTime> values = entry.getValue();
            Double meanValue = values.stream().filter(v-> StringUtils.isNotEmpty(v.getMeanCostStr())).collect(Collectors.averagingDouble(EventCodeCostTime::getMeanCost));
            System.out.printf("%s :", eventCode);
            System.out.printf("%s： %.1f%n", key, meanValue/1000);
        }
    }

    private Map<String, Object[][]> costProcess(Map<String, Object[][]> costData, Map<String, Object[][]> lastCostData){
        Map<String, Object[][]> excelDatas = new LinkedHashMap<>();
        for(Map.Entry<String, Object[][]> entry : costData.entrySet()){
            String key = entry.getKey();
            Object[][] thisWValues = entry.getValue();
            Object[][] lastWValues = lastCostData.get(key);
            Object[][] values = new Object[thisWValues.length][3];
            for (int i = 0; i < thisWValues.length; i++) {
                values[i][0] = thisWValues[i][0];
                values[i][1] = lastWValues[i][1];
                values[i][2] = thisWValues[i][1];
            }
            excelDatas.put(key, values);
        }
        return excelDatas;
    }
}
