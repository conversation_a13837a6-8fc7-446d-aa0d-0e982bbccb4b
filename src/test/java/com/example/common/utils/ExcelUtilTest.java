package com.example.common.utils;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.util.Units;
import org.apache.poi.xddf.usermodel.chart.*;
import org.apache.poi.xssf.usermodel.*;
import org.apache.poi.xwpf.usermodel.XWPFChart;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.junit.Test;
import org.openxmlformats.schemas.drawingml.x2006.chart.*;
import org.openxmlformats.schemas.drawingml.x2006.main.CTLineProperties;
import org.openxmlformats.schemas.drawingml.x2006.main.STPresetLineDashVal;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTPageMar;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTSectPr;

import javax.servlet.http.HttpServletResponse;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

public class ExcelUtilTest {

    @Test
    public void testGetHSSFWorkbook() throws Exception {
        Double[][] value = new Double[3][3];
        value[0][0] = 1.0;
        value[0][1] = 2.0;
        value[0][2] = 3.0;

        value[1][0] = 3.0;
        value[1][1] = 3.0;
        value[1][2] = 3.0;

        value[2][0] = 3.0;
        value[2][1] = 3.0;
        value[2][2] = 3.0;

        String[] title = {"学号", "姓名", "年龄"};
        String fileName = System.currentTimeMillis() + ".xls"; //文件名

//        ExcelUtil.exportFeedBack(fileName, value, title, "");
    }

    @Test
    // An highlighted block
    public void hydrographAndRainfallFigureWordExport(HttpServletResponse response) {
        //创建文本对象
        XWPFDocument document = new XWPFDocument();
        CTSectPr sectPr = document.getDocument().getBody().addNewSectPr();
        CTPageMar pageMar = sectPr.addNewPgMar();
        pageMar.setLeft(BigInteger.valueOf(720L));
        pageMar.setTop(BigInteger.valueOf(1440L));
        pageMar.setRight(BigInteger.valueOf(720L));
        pageMar.setBottom(BigInteger.valueOf(1440L));
        try {
            //共用X轴数据
            List<String> DateList = new ArrayList<>();
            DateList.add("2022-01-01");
            DateList.add("2022-01-02");
            DateList.add("2022-01-03");
            DateList.add("2022-01-04");
            DateList.add("2022-01-05");
            DateList.add("2022-01-06");
            String[] categories = {"2022-01-01","2022-01-02","2022-01-03","2022-01-04","2022-01-05","2022-01-06"};
            //柱状图Y轴数据
            List<Double> HistogramDataList = new ArrayList<>();
            HistogramDataList.add(1.74);
            HistogramDataList.add(2.31);
            HistogramDataList.add(0.65);
            HistogramDataList.add(1.42);
            HistogramDataList.add(2.00);
            HistogramDataList.add(1.73);
            Double[] HistogramvaluesA  ={1.74,2.31,0.65,1.42,2.00,1.73};
            //折线图Y轴数据
            List<Double> LineDataList = new ArrayList<>();
            LineDataList.add(1.74);
            LineDataList.add(2.31);
            LineDataList.add(0.65);
            LineDataList.add(1.42);
            LineDataList.add(2.00);
            LineDataList.add(1.73);
            Double[] LinevaluesA = {1.74,2.31,0.65,1.42,2.00,1.73} ;
//-----------------------------------------柱状图-------------------------------------------------
            // 设置图表大小
            XWPFChart chart = document.createChart(15 * Units.EMU_PER_CENTIMETER, 5 * Units.EMU_PER_CENTIMETER);
            //创建相关数据
            int numOfPoints = categories.length;
            String categoryDataRange = chart.formatRange(new CellRangeAddress(1, numOfPoints, 0, 0));
            String valuesDataRangeA = chart.formatRange(new CellRangeAddress(1, numOfPoints, 1, 1));
            XDDFDataSource<String> categoriesData = XDDFDataSourcesFactory.fromArray(categories, categoryDataRange, 1);
            XDDFNumericalDataSource<Double> valuesDataA = XDDFDataSourcesFactory.fromArray(HistogramvaluesA, valuesDataRangeA, 1);
            //创建X轴
            XDDFCategoryAxis bottomAxis = chart.createCategoryAxis(AxisPosition.TOP);
            // 左Y轴
            XDDFValueAxis leftAxis = chart.createValueAxis(AxisPosition.LEFT);
            // 左Y轴和X轴交叉点在X轴0点位置，在这里我直接注释掉了。
//          leftAxis.setCrosses(AxisCrosses.AUTO_ZERO);
//          leftAxis.setCrossBetween(AxisCrossBetween.BETWEEN);
            // 构建坐标轴
            leftAxis.crossAxis(bottomAxis);
            bottomAxis.crossAxis(leftAxis);
            //设置柱状图Y轴名称，方位和坐标轴大小
            leftAxis.setTitle("降雨量/mm");
            leftAxis.setCrosses(AxisCrosses.MAX);
            leftAxis.setCrossBetween(AxisCrossBetween.BETWEEN);
            // create series
            bottomAxis.setMajorTickMark(AxisTickMark.NONE);//取消X轴的标刻度
            //获取X轴 图表的基本配置都在这个对象里面里面
            CTCatAx catAx = chart.getCTChart().getPlotArea().getCatAxArray(0);
            CTSkip ctSkip = CTSkip.Factory.newInstance();
            //设置显示间隔
            ctSkip.setVal((int) Math.ceil(1));
            catAx.setTickLblSkip(ctSkip);
            //设置标签位置为最下
            CTTickLblPos ctTickLblPos = CTTickLblPos.Factory.newInstance();
            ctTickLblPos.setVal(STTickLblPos.LOW);
            catAx.setTickLblPos(ctTickLblPos);
            //获取Y轴 图表的基本配置都在这个对象里面里面
            CTValAx catAy = chart.getCTChart().getPlotArea().getValAxArray(0);
            CTScaling ctScaling ;
            ctScaling = catAy.addNewScaling();
            //设置柱状图Y轴坐标最大值
            ctScaling.addNewMax().setVal(8);

            ctScaling.addNewOrientation().setVal(STOrientation.MAX_MIN);
            catAy.setScaling(ctScaling);
            // 设置图表背后的网格线
            CTLineProperties ctLine = catAy.addNewMajorGridlines().addNewSpPr().addNewLn();
            ctLine.addNewPrstDash().setVal(STPresetLineDashVal.DASH);

            //创建柱状图数据对象
            XDDFChartData data = chart.createData(ChartTypes.BAR, bottomAxis, leftAxis);
            ((XDDFBarChartData) data).setBarDirection(BarDirection.COL);
            //柱状图图例标题
            XDDFChartData.Series series = data.addSeries(categoriesData, valuesDataA);
            series.setTitle("下雨量", setTitleInDataSheet(chart, "", 0));
            chart.plot(data);
//-----------------------------------------折线图-------------------------------------------------
            // 右Y轴
            XDDFValueAxis rightAxis = chart.createValueAxis(AxisPosition.RIGHT);
            // 右Y轴和X轴交叉点在X轴最大值位置
            rightAxis.setCrosses(AxisCrosses.MIN);
            rightAxis.setCrossBetween(AxisCrossBetween.BETWEEN);
            // 构建坐标轴
            rightAxis.crossAxis(bottomAxis);
            bottomAxis.crossAxis(rightAxis);
            //设置折线图Y轴名称
            rightAxis.setTitle("水位/m");
            XDDFCategoryDataSource countries = XDDFDataSourcesFactory.fromArray(categories);
            //设置折线图Y轴坐标最大值
            rightAxis.setMaximum(8);
            //LINE：折线图，
            data = chart.createData(ChartTypes.LINE, bottomAxis, rightAxis);
            //加载折线图数据
            XDDFNumericalDataSource<Double> area = XDDFDataSourcesFactory.fromArray(LinevaluesA);
            //图表加载数据，折线1
            XDDFLineChartData.Series series1 = (XDDFLineChartData.Series) data.addSeries(countries, area);
            //折线图例标题
            series1.setTitle("水位", null);
            //直线
            series1.setSmooth(true);
            //设置标记大小
            series1.setMarkerSize((short) 2);
            //设置空数据显示间隙
            CTDispBlanksAs disp = CTDispBlanksAs.Factory.newInstance();
            disp.setVal(STDispBlanksAs.GAP);
            chart.getCTChart().setDispBlanksAs(disp);
            data.setVaryColors(false);
            //绘制
            chart.plot(data);
            //设置图表图例
            XDDFChartLegend legend = chart.getOrAddLegend();
            legend.setPosition(LegendPosition.TOP);
            //生成word文件，设置文件相关信息。
            response.setContentType("application/force-download");// 设置强制下载不打开
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("折线+柱状组合图.docx", "UTF-8"));
            OutputStream out = response.getOutputStream();
            document.write(out);
            out.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    //在WORD内生成柱状图所需方法
    static CellReference setTitleInDataSheet(XWPFChart chart, String title, int column) {
        try {
            XSSFWorkbook workbook = null;
            workbook = chart.getWorkbook();
            XSSFSheet sheet = workbook.getSheetAt(0);
            XSSFRow row = sheet.getRow(0);
            if (row == null)
                row = sheet.createRow(0);
            XSSFCell cell = row.getCell(column);
            if (cell == null)
                cell = row.createCell(column);
            cell.setCellValue(title);
            return new CellReference(sheet.getSheetName(), 0, column, true, true);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    public static void main(String[] args) throws IOException {
        XSSFWorkbook wb = new XSSFWorkbook();
        String sheetName = "Sheet1";
        FileOutputStream fileOut = null;
        try {
            XSSFSheet sheet = wb.createSheet(sheetName);
            //第一行，国家名称
            Row row = sheet.createRow(0);
            Cell cell = row.createCell(0);
            cell.setCellValue("俄罗斯");
            cell = row.createCell(1);
            cell.setCellValue("加拿大");
            cell = row.createCell(2);
            cell.setCellValue("美国");
            cell = row.createCell(3);
            cell.setCellValue("中国");
            cell = row.createCell(4);
            cell.setCellValue("巴西");
            cell = row.createCell(5);
            cell.setCellValue("澳大利亚");
            cell = row.createCell(6);
            cell.setCellValue("印度");
            // 第二行，乡村地区
            row = sheet.createRow(1);
            cell = row.createCell(0);
            cell.setCellValue(17098242);
            cell = row.createCell(1);
            cell.setCellValue(9984670);
            cell = row.createCell(2);
            cell.setCellValue(9826675);
            cell = row.createCell(3);
            cell.setCellValue(9596961);
            cell = row.createCell(4);
            cell.setCellValue(8514877);
            cell = row.createCell(5);
            cell.setCellValue(7741220);
            cell = row.createCell(6);
            cell.setCellValue(3287263);
            // 第三行，农村人口
//            row = sheet.createRow(2);
//            cell = row.createCell(0);
//            cell.setCellValue(14590041);
//            cell = row.createCell(1);
//            cell.setCellValue(35151728);
//            cell = row.createCell(2);
//            cell.setCellValue(32993302);
//            cell = row.createCell(3);
//            cell.setCellValue(14362887);
//            cell = row.createCell(4);
//            cell.setCellValue(21172141);
//            cell = row.createCell(5);
//            cell.setCellValue(25335727);
//            cell = row.createCell(6);
//            cell.setCellValue(13724923);
            // 第四行，面积平局
//            row = sheet.createRow(3);
//            cell = row.createCell(0);
//            cell.setCellValue(9435701.143);
//            cell = row.createCell(1);
//            cell.setCellValue(9435701.143);
//            cell = row.createCell(2);
//            cell.setCellValue(9435701.143);
//            cell = row.createCell(3);
//            cell.setCellValue(9435701.143);
//            cell = row.createCell(4);
//            cell.setCellValue(9435701.143);
//            cell = row.createCell(5);
//            cell.setCellValue(9435701.143);
//            cell = row.createCell(6);
//            cell.setCellValue(9435701.143);
            // 第四行，人口平局
//            row = sheet.createRow(4);
//            cell = row.createCell(0);
//            cell.setCellValue(22475821.29);
//            cell = row.createCell(1);
//            cell.setCellValue(22475821.29);
//            cell = row.createCell(2);
//            cell.setCellValue(22475821.29);
//            cell = row.createCell(3);
//            cell.setCellValue(22475821.29);
//            cell = row.createCell(4);
//            cell.setCellValue(22475821.29);
//            cell = row.createCell(5);
//            cell.setCellValue(22475821.29);
//            cell = row.createCell(6);
//            cell.setCellValue(22475821.29);

            //创建一个画布
            XSSFDrawing drawing = sheet.createDrawingPatriarch();
            //前四个默认0，[0,5]：从0列5行开始;[7,26]:宽度7个单元格，26向下扩展到26行
            //默认宽度(14-8)*12
            XSSFClientAnchor anchor = drawing.createAnchor(0, 0, 0, 0, 0, 5, 7, 26);
            //创建一个chart对象
            XSSFChart chart = drawing.createChart(anchor);
            //标题
            chart.setTitleText("地区排名前七的国家");
            //标题覆盖
            chart.setTitleOverlay(false);

            //图例位置
            XDDFChartLegend legend = chart.getOrAddLegend();
            legend.setPosition(LegendPosition.TOP);

            //分类轴标(X轴),标题位置
            XDDFCategoryAxis bottomAxis = chart.createCategoryAxis(AxisPosition.BOTTOM);
            bottomAxis.setTitle("国家");
            //值(Y轴)轴,标题位置
            XDDFValueAxis leftAxis = chart.createValueAxis(AxisPosition.LEFT);
            leftAxis.setTitle("面积和人口");

            //LINE：折线图，
            XDDFLineChartData data = (XDDFLineChartData) chart.createData(ChartTypes.LINE, bottomAxis, leftAxis);

            //CellRangeAddress(起始行号，终止行号， 起始列号，终止列号）
            //分类轴标(X轴)数据，单元格范围位置[0, 0]到[0, 6]
            XDDFDataSource<String> countries = XDDFDataSourcesFactory.fromStringCellRange(sheet,
                    new CellRangeAddress(0, 0, 0, 6));
            //数据1，单元格范围位置[1, 0]到[1, 6]
            XDDFNumericalDataSource<Double> area = XDDFDataSourcesFactory.fromNumericCellRange(sheet,
                    new CellRangeAddress(1, 1, 0, 6));

            //数据1，单元格范围位置[2, 0]到[2, 6]
//            XDDFNumericalDataSource<Double> population = XDDFDataSourcesFactory.fromNumericCellRange(sheet,
//                    new CellRangeAddress(2, 2, 0, 6));

            //图表加载数据，折线1
            XDDFLineChartData.Series series1 = (XDDFLineChartData.Series) data.addSeries(countries, area);
            //折线图例标题
            series1.setTitle("面积", null);
            //直线
            series1.setSmooth(false);
            //设置标记大小
            series1.setMarkerSize((short) 6);
            //设置标记样式，星星
            series1.setMarkerStyle(MarkerStyle.STAR);

//            //图表加载数据，折线2
//            XDDFLineChartData.Series series2 = (XDDFLineChartData.Series) data.addSeries(countries, population);
//            //折线图例标题
//            series2.setTitle("人口", null);
//            //曲线
//            series2.setSmooth(true);
//            //设置标记大小
//            series2.setMarkerSize((short) 6);
//            //设置标记样式，正方形
//            series2.setMarkerStyle(MarkerStyle.SQUARE);
//
//            //图表加载数据，平均线3
//            //数据1，单元格范围位置[2, 0]到[2, 6]
//            XDDFNumericalDataSource<Double> population3 = XDDFDataSourcesFactory.fromNumericCellRange(sheet,
//                    new CellRangeAddress(3, 3, 0, 6));
//            XDDFLineChartData.Series series3 = (XDDFLineChartData.Series) data.addSeries(countries, population3);
//            //折线图例标题
//            series3.setTitle("面积平均", null);
//            //直线
//            series3.setSmooth(false);
//            //设置标记大小
//            series3.setMarkerSize((short) 3);
//            //设置标记样式，正方形
//            series3.setMarkerStyle(MarkerStyle.NONE);
//            //折线图LineChart
//            XDDFLineProperties line = new XDDFLineProperties();
//            line.setPresetDash(new XDDFPresetLineDash(PresetLineDash.DOT));//虚线
//            series3.setLineProperties(line);
//
//            //图表加载数据，平均线3
//            //数据1，单元格范围位置[2, 0]到[2, 6]
//            XDDFNumericalDataSource<Double> population4 = XDDFDataSourcesFactory.fromNumericCellRange(sheet,
//                    new CellRangeAddress(4, 4, 0, 6));
//            XDDFLineChartData.Series series4 = (XDDFLineChartData.Series) data.addSeries(countries, population4);
//            //折线图例标题
//            series4.setTitle("人口平均", null);
//            //直线
//            series4.setSmooth(false);
//            //设置标记大小
//            //设置标记样式，正方形
//            series4.setMarkerStyle(MarkerStyle.NONE);
//            XDDFLineProperties line4 = new XDDFLineProperties();
//            line4.setPresetDash(new XDDFPresetLineDash(PresetLineDash.DOT));//虚线
//            series4.setLineProperties(line);

            //绘制
            chart.plot(data);

            // 将输出写入excel文件
            String filename = "排行榜前七的国家.xlsx";
            fileOut = new FileOutputStream(filename);
            wb.write(fileOut);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            wb.close();
            if (fileOut != null) {
                fileOut.close();
            }
        }

    }
}
