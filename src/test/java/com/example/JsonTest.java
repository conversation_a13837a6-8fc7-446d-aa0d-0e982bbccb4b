package com.example;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.example.common.utils.FileUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class JsonTest {
    private static final Logger LOGGER = LoggerFactory.getLogger(JsonTest.class);

    private static Map<String,Object> queryDataInDataVoV2(Map dataVo, String dataJoinPath, String dependCode, int preType){
        Map<String,Object> result = new HashMap<>();
        Object dataIter = "";
        for (String subPath : dataJoinPath.split("\\|")) {
            try {
                dataIter = dataVo;
                String[] items = subPath.split("@");
                for (int i = 0; i < items.length; i++) {
                    String jsonPath = "$" + items[i].replace("/", ".");
                    if (i > 0) {
                        dataIter = JSON.parse(dataIter.toString());
                    }
                    if (JSONPath.contains(dataIter, jsonPath)) {
                        dataIter = JSONPath.eval(dataIter, jsonPath);
                    } else {
                        LOGGER.info("jsonPathNotExists {}", jsonPath);
                        dataIter = "";
                        break;
                    }
                }
                //跳出或条件判断
                if(StringUtils.isNotBlank(dataIter.toString())){
                    break;
                }
            } catch (Exception e) {
                LOGGER.warn("getSingleDataFromDataVoByPath fail", e);
                dataIter = "";
            }
        }

        try{
            if(dataIter instanceof Map){
                dataIter = JSON.toJSONString(dataIter);
            }
            dataIter = JSON.parseObject(dataIter.toString());
        }catch (Exception e){
            LOGGER.warn("dataJoinPath={} data={}", dataJoinPath, StringUtils.abbreviate(dataIter.toString(), 1024), e);
            try{
                dataIter = JSON.parseArray(dataIter.toString());
            }catch (Exception ex){
                LOGGER.warn("preType = " + preType + ";dataIter error", ex);
            }
        }
        result.put(dependCode, dataIter);
        return result;
    }


    public static void main(String[] args) throws IOException {
        final String dataStr = FileUtil.toOneLine("data.json");
        JSONObject dataVo = JSONObject.parseObject(dataStr);
        String dataJoinPath = "/thirdPartyData/allPlistService|/allPlistService";// "/thirdPartyData/BAIRONG|/thirdPartyData/BAI_RONG|/thirdPartyData/bairongService";
        String dependCode = "allPlistService"; // "bairongService";
        queryDataInDataVoV2(dataVo, dataJoinPath, dependCode, 6);
//        String d = "{\"data\":\"{\\\"als_m12_id_max_monnum\\\":\\\"7\\\"}\"}";
//        System.out.println(JSON.parseObject(d.toString()));
    }
}
