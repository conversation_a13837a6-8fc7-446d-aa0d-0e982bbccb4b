package com.example;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class Test {
    public static void main(String[] args) throws ParseException {
        System.out.println(new Date());
        //精确到毫秒  output: 2020-03-21 21:07:41 468
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        System.out.println(sdf.format(new Date()));
        System.out.println(sdf.format(System.currentTimeMillis()));
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        Date currentTime = formatter.parse(formatter.format(new Date()));
        System.out.println(currentTime);
        Date now = new Timestamp(System.currentTimeMillis());
        System.out.println(now);
    }
}
