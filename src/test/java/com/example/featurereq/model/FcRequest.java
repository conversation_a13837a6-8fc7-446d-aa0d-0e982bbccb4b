package com.example.featurereq.model;

import java.io.Serializable;
import java.util.List;


public class FcRequest implements Serializable {

    private static final long serialVersionUID = 8503200054840734327L;
    private String sourceSystem;
    private String loanKey;
    private String step;
    private String eventCode;
    private Object dataVo;
    private String dataVoFormat;
    private List<Feature> featureList;

    public String getSourceSystem() {
        return this.sourceSystem;
    }

    public void setSourceSystem(String sourceSystem) {
        this.sourceSystem = sourceSystem;
    }

    public String getLoanKey() {
        return this.loanKey;
    }

    public void setLoanKey(String loanKey) {
        this.loanKey = loanKey;
    }

    public List<Feature> getFeatureList() {
        return this.featureList;
    }

    public void setFeatureList(List<Feature> featureList) {
        this.featureList = featureList;
    }

    public Object getDataVo() {
        return this.dataVo;
    }

    public void setDataVo(Object dataVo) {
        this.dataVo = dataVo;
    }

    public String getDataVoFormat() {
        return dataVoFormat;
    }

    public void setDataVoFormat(String dataVoFormat) {
        this.dataVoFormat = dataVoFormat;
    }

    public String getStep() {
        return step;
    }

    public void setStep(String step) {
        this.step = step;
    }

    public String getEventCode() {
        return eventCode;
    }

    public void setEventCode(String eventCode) {
        this.eventCode = eventCode;
    }

    public static class Feature implements Serializable {
        private static final long serialVersionUID = 4531219294496656999L;
        private String featureName;
        private Long codeId;
        private String featureVersion;
        private String pythonVersion;
        private String dataType;

        public String getFeatureName() {
            return this.featureName;
        }

        public void setFeatureName(String featureName) {
            this.featureName = featureName;
        }

        public Long getCodeId() {
            return this.codeId;
        }

        public void setCodeId(Long codeId) {
            this.codeId = codeId;
        }

        public String getFeatureVersion() {
            return this.featureVersion;
        }

        public void setFeatureVersion(String featureVersion) {
            this.featureVersion = featureVersion;
        }

        public String getPythonVersion() {
            return this.pythonVersion;
        }

        public void setPythonVersion(String pythonVersion) {
            this.pythonVersion = pythonVersion;
        }

        public String getDataType() {
            return this.dataType;
        }

        public void setDataType(String dataType) {
            this.dataType = dataType;
        }
    }

}

