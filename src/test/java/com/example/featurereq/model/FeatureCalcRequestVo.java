package com.example.featurereq.model;

import java.util.List;
import java.util.Map;

public class FeatureCalcRequestVo {


    private String sourceSystem;

    private List<FeatureReqVo> featureList;

    private Object dataVo;

    private String xml;

    private Long policyCodeId;

    private String policyCode;

    private String loanKey;

    private String type;

    private String eventCode;

    private String step;

    /**
     * 该字段，只在衍生特征计算时会设置值，用于记录层级，打点用
     */
    private int drLevel;

    public void checkParams() {
        if (sourceSystem == null) {
            throw new IllegalArgumentException("sourceSystem is null");
        }
        if (featureList == null || featureList.isEmpty()) {
            throw new IllegalArgumentException("featureList is empty");
        }
        if (dataVo == null) {
            throw new IllegalArgumentException("dataVo is null");
        }

        if (dataVo instanceof Map) {
            Map data = (Map) dataVo;
            if (data.isEmpty()) {
                throw new IllegalArgumentException("dataVo is empty");
            }
        }
    }
    

    public String getLoanKey() {
        return this.loanKey;
    }

    public void setLoanKey(String loanKey) {
        this.loanKey = loanKey;
    }

    public String getSourceSystem() {
        return this.sourceSystem;
    }

    public void setSourceSystem(String sourceSystem) {
        this.sourceSystem = sourceSystem;
    }

    public List<FeatureReqVo> getFeatureList() {
        return this.featureList;
    }

    public void setFeatureList(List<FeatureReqVo> featureList) {
        this.featureList = featureList;
    }

    public Object getDataVo() {
        return this.dataVo;
    }

    public void setDataVo(Object dataVo) {
        this.dataVo = dataVo;
    }

    public String getXml() {
        return this.xml;
    }

    public void setXml(String xml) {
        this.xml = xml;
    }

    public Long getPolicyCodeId() {
        return this.policyCodeId;
    }

    public void setPolicyCodeId(Long policyCodeId) {
        this.policyCodeId = policyCodeId;
    }

    public String getPolicyCode() {
        return this.policyCode;
    }

    public void setPolicyCode(String policyCode) {
        this.policyCode = policyCode;
    }


    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getEventCode() {
        return eventCode;
    }

    public void setEventCode(String eventCode) {
        this.eventCode = eventCode;
    }

    public String getStep() {
        return step;
    }

    public void setStep(String step) {
        this.step = step;
    }

    public int getDrLevel() {
        return drLevel;
    }

    public void setDrLevel(int drLevel) {
        this.drLevel = drLevel;
    }

    @Override
    public String toString() {
        return "FeatureCalcRequestVo{" +
                "sourceSystem='" + sourceSystem + '\'' +
                ", featureList=" + featureList +
                ", dataVo=" + dataVo +
                ", xml='" + xml + '\'' +
                ", policyCodeId=" + policyCodeId +
                ", policyCode='" + policyCode + '\'' +
                ", loanKey='" + loanKey + '\'' +
                ", type='" + type + '\'' +
                ", eventCode='" + eventCode + '\'' +
                ", step='" + step + '\'' +
                ", drLevel=" + drLevel + '\'' +
                '}';
    }
}
