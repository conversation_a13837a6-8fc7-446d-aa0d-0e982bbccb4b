package com.example.featurereq.model;

public class FeatureReqVo {

    private String featureName;
    private Integer featureVersion;
    private String dataType;

    private String pythonVersion;

    private Long codeId;

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private String featureName;
        private Integer featureVersion;
        private String dataType;
        private String pythonVersion;
        private Long codeId;

        public Builder featureName(String featureName) {
            this.featureName = featureName;
            return this;
        }

        public Builder featureVersion(Integer featureVersion) {
            this.featureVersion = featureVersion;
            return this;
        }

        public Builder pythonVersion(String pythonVersion) {
            this.pythonVersion = pythonVersion;
            return this;
        }

        public Builder codeId(Long codeId) {
            this.codeId = codeId;
            return this;
        }

        public Builder dataType(String dataType) {
            this.dataType = dataType;
            return this;
        }


        public FeatureReqVo build() {
            FeatureReqVo reqVo = new FeatureReqVo();
            reqVo.setFeatureName(featureName);
            reqVo.setCodeId(codeId);
            reqVo.setFeatureVersion(featureVersion);
            reqVo.setDataType(dataType);
            reqVo.setPythonVersion(pythonVersion);
            return reqVo;
        }
    }

    public String getFeatureName() {
        return this.featureName;
    }

    public void setFeatureName(String featureName) {
        this.featureName = featureName;
    }

    public Long getCodeId() {
        return this.codeId;
    }

    public void setCodeId(Long codeId) {
        this.codeId = codeId;
    }

	public String getPythonVersion() {
		return pythonVersion;
	}

	public void setPythonVersion(String pythonVersion) {
		this.pythonVersion = pythonVersion;
	}


    public Integer getFeatureVersion() {
        return featureVersion;
    }

    public void setFeatureVersion(Integer featureVersion) {
        this.featureVersion = featureVersion;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }
}
