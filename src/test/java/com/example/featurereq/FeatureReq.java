package com.example.featurereq;

import com.alibaba.fastjson.JSON;
import com.example.featurereq.model.FcRequest;
import com.example.featurereq.model.FeatureCalcRequestVo;

import java.util.ArrayList;
import java.util.List;

public class FeatureReq {
    public static void main(String[] args) {
        FcRequest req = new FcRequest();
        req.setDataVo("");
        req.setDataVoFormat("xml");
        req.setStep("HH_PRELOAN");
        req.setEventCode("hhPreLoanAudit");
        req.setLoanKey("hh_111111111111");
        req.setSourceSystem("HAO_HUAN");
        List<FcRequest.Feature> list = new ArrayList<>();
        FcRequest.Feature f = new FcRequest.Feature();
        f.setFeatureName("a_sd");
        f.setFeatureVersion("5");
        f.setCodeId(100L);
        f.setDataType("3");
        f.setPythonVersion("python3");
        list.add(f);
        req.setFeatureList(list);
        String s = JSON.toJSONString(req);
        FeatureCalcRequestVo featureCalcRequestVo = JSON.parseObject(s, FeatureCalcRequestVo.class);
        System.out.println(featureCalcRequestVo);
    }
}
