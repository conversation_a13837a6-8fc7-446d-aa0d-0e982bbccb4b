package com.example;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.example.common.utils.FileUtil;
import com.jayway.jsonpath.JsonPath;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class JsonPathTest {
    private static final Logger LOGGER = LoggerFactory.getLogger(JsonPathTest.class);

    private static void queryDataInDataVoV2(Object dataVo, String jsonPath){
        Object read = JsonPath.read(dataVo, jsonPath);
        LOGGER.info("datavo: {}", read);
//        if (JSONPath.contains(dataVo, jsonPath)) {
//            dataVo = JSONPath.eval(dataVo, jsonPath);
//            LOGGER.info("datavo: {}", dataVo);
//        } else {
//            LOGGER.info("jsonPathNotExists {}", jsonPath);
//        }
    }


    public static void main(String[] args) throws IOException {
        final String dataStr = FileUtil.toOneLine("data.json");
        JSONObject dataVo = JSONObject.parseObject(dataStr);
//        String dataJoinPath = "$.personal.['fullName','firstName']"; // "$.personal.[?(!@.email)]";
        String dataJoinPath = "$.personal.[?(@.email)]";
        queryDataInDataVoV2(dataVo, dataJoinPath);
//        String d = "{\"data\":\"{\\\"als_m12_id_max_monnum\\\":\\\"7\\\"}\"}";
//        System.out.println(JSON.parseObject(d.toString()));
    }
}
