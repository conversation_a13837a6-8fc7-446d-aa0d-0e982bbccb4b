package com.example.strategy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;

public class StrategyResultTest {
    public static void main(String[] args) {
        String s = "{\"reason_code\": {\"model_202107_alln_rmpaic_bin\": 2.0, \"flightScore\": -1.0, \"agent_name\": \"今日头条-乐推-LA8\", \"GBrules\": \"B01\", \"educationlevel_increase_tag\": 0.0, \"low_edu\": 0, \"level_flag\": \"202107rmpaicDB_level\", \"als_m3_id_nbank_orgnum\": 2.0, \"als_m3_id_nbank_orgnum_recall\": 2.0, \"house_loan_amount\": \"(-INF,0]\", \"events\": {\"0007\": {\"tag\": \"loan_temp_line_001_geta\"}, \"0014\": {\"tag\": \"loan_rate_003_lowr\"}}, \"quota_score\": {\"quota_score_td_score_prob\": -0.****************, \"quota_prob\": 0.****************, \"quota_score_dhb_union_score\": -0.*****************, \"quota_score_umeng_tags\": -0.*****************, \"quota_score_umeng_creditscore\": 0.*****************, \"quota_score_alm_d15_id_nbank_oth_orgnum\": -0.*****************, \"quota_score_industry\": 0.0008740227430830937, \"quota_score_source_type\": -0.****************, \"quota_score_tc_cpl0080\": 0.045912478784285525, \"quota_score_tx_af_riskscore\": -0.*****************, \"quota_score_age\": 0.*****************, \"quota_score_duxiaomancreditscore\": -0.*****************, \"quota_score_qy_score\": -0.*****************, \"quota_score_mix_income\": -0.*****************, \"quota_score_afp1090_zzmodeliscore\": 0.*****************, \"quota_score_dhb_fristcontact_status\": -0.*****************, \"quota_score_incomming\": 0.*****************, \"quota_score_fpd_score_v2\": -0.****************, \"quota_score_bw_fraudscore\": 0.*****************, \"quota_score_active\": 0.*****************, \"quota_score\": 621.*************, \"quota_score_id_province\": 0.*****************}, \"fs_hjdt\": 0.0, \"manual_tag_random\": 0.****************, \"is_tail_customer\": 1.0, \"source_type\": \"信息流\", \"duXiaoManCreditScore\": 508.0, \"fxfs\": -1.0, \"creditGrantingAmounttestlift\": 4800.0, \"m1_self_count\": \"0\", \"irrtag\": \"0\", \"ma_score_fpdv5_prob_recall\": 0.****************, \"is_demote\": 0, \"rp_random19\": 0.16184919013, \"hujin_prob_recall\": 0.************, \"BaseAmount\": 6000, \"userLevel\": \"B\", \"dxm_group\": 1.0, \"fundPaymentAmt\": \"(1000,2000]\", \"high_edu_income\": 0, \"quotascore_good_flag\": 1.0, \"swap_in_flag\": \"0\", \"tmp_param2\": 0.0, \"tmp_param1\": 0.0, \"all_model_combine_bin\": 5.0, \"random46\": 0.************, \"eduexperience_recall\": \"30\", \"weicheng_level\": \"D\", \"umeng_group\": 4.0, \"loanorgcount\": 2.0, \"is_self_count\": 0.0, \"AB_test\": \"A_HigherCoef\", \"house_debt_amt_increase_tag\": 0.0, \"als_m3_id_nbank_orgnum_lift\": 2.0, \"fs_source_type\": 0.0, \"rhzx_increase_group\": \"lower\", \"is_irr\": 3, \"new_tag\": \"new_coming\", \"ma_score_mob6score_202206_recall\": 608.0, \"qy_score_recall\": 52.06, \"base_amt_grp\": \"higher\", \"rhzx_increase_coef\": 1.0, \"userLevel_laohui\": \"None\", \"yxgrade1_bin_increase_tag\": 0.0, \"xs9_fxfs\": 1.0, \"s_bh_group\": 1.0, \"channel_code\": \"tt-LT-LA8-36\", \"instable_marriage\": 0, \"good_rule_hit\": 0.0, \"shop_part_1_final_round\": 4800.0, \"house_debt_amt\": \"-1\", \"pangu_group\": 2.0, \"rhzx_increase_tag\": 0.0, \"yxGrade3_edu_level_strategy\": \"30-大专\", \"random4\": 0.************, \"fundpaymentamt_increase_tag\": 0.0, \"qy_group\": \"2\", \"rrd_amount\": -999.0, \"quotascore_coef\": \"1.0\", \"fs_all_model_combine_bin\": -1.0, \"raw_level\": \"B\", \"is_duotou\": 0.0, \"shopAmount\": 4800.0, \"model_tag\": \"202107rmpaicModelDB\", \"fs_hit_tag\": 0.0, \"is_reloan\": 0, \"is_outstandcount\": 0.0, \"utmsource\": \"None\", \"rp_random3\": 0.************, \"is_postback_C\": 0.0, \"zhishu_income\": \" \", \"amount_rate_test\": \"test_0403\", \"dtadjustv2_rmpaic_bin\": 1.0, \"model_combine_userlesvl\": \"A\", \"whiteBox_version\": \"v3\", \"tianchuang_model_prob_recall\": 0.*************, \"is_highline\": 0.0, \"random12\": 0.************, \"bt_frozen_allLoan\": \"active\", \"fs_edu\": 0.0, \"is_postback_B\": \"0.0\", \"zz_rank_7Bin\": 2.0, \"is_postback_A\": \"0.0\", \"als_m1_id_nbank_orgnum_recall\": 2.0, \"zhishu_income_tag\": \"lower_income_seg\", \"educationLevel\": \"-1\", \"creditGrantingAmountlift\": 4800.0, \"als_m6_id_nbank_orgnum_lift\": 4.0, \"is_hj_loanorg\": 0.0, \"GB_group\": 6.0, \"fpd_score_recall\": 0.020185130485244344, \"alias\": \"XXL_JRTT-LT-LA8\", \"bw_score_recall\": 69.87, \"is_lh_rate\": 1, \"userPoint\": 450.0, \"raw_level_group\": 2.0, \"ma_score_mob3ascore_202206_recall\": 0.****************, \"bwf_group\": \"3\", \"fund_payment_amount\": \"-999\", \"final_laohui_tag\": 0.0, \"is_sanfang_tail\": 0.0, \"channel_coef\": 0.8, \"outstandcount\": 2.0, \"loan_part_1_final_round\": 4800.0, \"fs_7bin\": 0.0, \"202107DB_base_amt_grp\": \"normal\", \"flow_tag\": \"老策略\", \"newbaseamount20221025\": 4800.0, \"age\": 34.58, \"tag_details\": {\"add_loan_flag\": true, \"add_loan_increased_limit\": 2000, \"add_shop_flag\": false, \"add_shop_increased_limit\": 0, \"add_loan_decreased_rate\": 0, \"reloan_increased_limit\": 0, \"reloan_decreased_rate\": 0}, \"increased_details\": {\"reloan_increased_limit\": [], \"reloan_increased_level\": [], \"reloan_increased_time\": [], \"add_increased_limit\": [2000], \"add_increased_level\": [], \"add_increased_time\": [\"2022-11-24\"]}, \"recall_reference_data\": {\"user_level\": \"B\", \"days_diff\": 1, \"br_income\": -1, \"zhishu_income\": \" \", \"income_level\": \"3+\", \"test_num\": 0.5970158034538708}, \"recall_result\": {\"Tetag\": \"-1\", \"line_re\": {\"lift_amount\": 2000, \"lift_type\": {\"lift_type\": \"tmp\", \"lift_interval\": 29}, \"lift_strategy\": \"life_cycle_credit_d1_40_recall\", \"lift_details\": {\"user_level\": \"B\", \"loan_line\": 4800.0, \"lift_rate\": 0.4, \"lift_amount\": 2000, \"lift_flag\": true, \"lift_strategy\": \"life_cycle_credit_d1_40_recall\", \"lift_time\": \"2022-11-24\"}}, \"rate_re\": {\"rate\": 0.0134, \"rate_strategy\": \"life_cycle_credit_d1_recall\", \"rate_details\": {\"days_diff\": 1, \"user_level\": \"B\", \"income_level\": \"3+\", \"test_num\": 0.5970158034538708}}, \"test_tag_d1\": \"line_lift_40\", \"test_tag_d5\": \"Null\", \"test_tag_d10\": \"Null\", \"test_tag_d1_new\": \"test\", \"test_tag_d5_new\": \"Null\", \"test_tag_d10_new\": \"Null\", \"ind_bad\": 0, \"ind_good1\": 1, \"ind_good2\": 0, \"ind_common\": 0, \"ind_good3\": 0, \"is_irr\": 3, \"cus_tag_recall_t1\": \"04-good_zhuankeordiduotou\", \"strategy_type\": \"AMOUNT_ASSIGN_APR_ALL\"}}, \"user_level\": \"B\", \"credit_line\": 6800.0, \"avail_line\": 6800.0, \"util_line\": 0.0, \"loan_id\": 44872283, \"loan_line\": 6800.0, \"loan_avail_line\": 6800.0, \"loan_actual_line\": 6800.0, \"loan_util_line\": 0.0, \"loan_rate\": 0.0112, \"loan_period\": 12, \"bt_line\": 0.0, \"bt_avail_line\": 0.0, \"bt_actual_line\": 0.0, \"bt_util_line\": 0.0, \"bt_rate\": 0.03, \"bt_period\": 0, \"shop_line\": 4800.0, \"shop_avail_line\": 4800.0, \"shop_actual_line\": 4800.0, \"shop_util_line\": 0.0, \"shop_rate\": 0.0179, \"shop_period\": 6, \"period_line_rate\": [{\"rate_yn\": 0.0134, \"min\": 500.0, \"max\": 6800.0, \"period\": 3, \"guarantee_rate\": 0.019, \"rate\": 0.0134, \"type\": \"loan\"}, {\"lh_rate_period\": 4, \"rate_yn\": 0.0118, \"lh_rate\": 0.0126, \"min\": 500.0, \"max\": 6800.0, \"period\": 6, \"guarantee_rate\": 0.019, \"rate\": 0.0118, \"is_lh_rate\": 1, \"type\": \"loan\"}, {\"lh_rate_period\": 4, \"rate_yn\": 0.0114, \"lh_rate\": 0.0123, \"min\": 500.0, \"max\": 6800.0, \"period\": 9, \"guarantee_rate\": 0.016, \"rate\": 0.0114, \"is_lh_rate\": 1, \"type\": \"loan\"}, {\"lh_rate_period\": 4, \"rate_yn\": 0.0112, \"lh_rate\": 0.0153, \"min\": 500.0, \"max\": 6800.0, \"period\": 12, \"guarantee_rate\": 0.0135, \"rate\": 0.0112, \"is_lh_rate\": 1, \"type\": \"loan\"}, {\"lh_rate_period\": 4, \"rate_yn\": 0.0179, \"lh_rate\": 0.0179, \"min\": 1.0, \"max\": 4800.0, \"period\": 6, \"guarantee_rate\": 0.019, \"rate\": 0.0179, \"is_lh_rate\": 1, \"type\": \"shop\"}], \"account_status\": \"B\", \"is_closed\": false, \"user_point\": 450.0, \"line_assign_time\": \"2022-11-24 07:00:04\", \"test_code\": {}, \"status\": true, \"segment_code\": {}, \"ext1\": [{\"tmp_line_status\": 1, \"tmp_actual_line\": 0.0, \"tmp_line_end_time\": \"*************\", \"fix_line\": 4800.0, \"tmp_line\": 2000, \"tmp_avail_line\": 2000.0, \"type\": 1, \"tmp_util_line\": 0.0}, {\"tmp_line_status\": 0, \"tmp_actual_line\": 0.0, \"tmp_line_end_time\": \" \", \"fix_line\": 4800.0, \"tmp_line\": 0, \"tmp_avail_line\": 0.0, \"type\": 2, \"tmp_util_line\": 0.0}], \"strategy_type\": \"AMOUNT_ASSIGN_APR_ALL\", \"amount_period_rate_management\": {}, \"loan_key\": \"hh_e5b99980-70d2-48a2-8992-1938c7cb4c3e\", \"user_key\": \"ffa0cf4eabf880142fea9d101701a1a0\", \"update_time\": \"**************\", \"id\": **********, \"is_active\": true, \"create_time\": \"**************\", \"source_system\": \"HAO_HUAN\", \"strategy_id\": 15451, \"sharding_table_name\": \"verify_user_line_management_47\", \"version\": 1}";
        String s1 = JSON.toJSONString(s, SerializerFeature.WriteMapNullValue);
        System.out.println(s1);
    }
}
