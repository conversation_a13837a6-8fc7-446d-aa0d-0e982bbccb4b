package com.example.stream;

import java.util.Arrays;
import java.util.List;

public class StreamTest {

    private static final List<String> EXCLUDE_URLS = Arrays.asList("/multiUpload", "/a");

    public static void main(String[] args) {
        boolean b = EXCLUDE_URLS.stream()
                .anyMatch("http://cp-web.weicai.com.cn/features/multiUpload"::endsWith);
        System.out.println(b);
    }
}
