开始时间(本周): 2023-05-31 12:00:00 结束时间(本周): 2023-05-31 15:00:00 开始时间(上周): 2023-05-17 12:00:00 结束时间(上周): 2023-05-17 15:00:00
【本周】===============================================================================================
haoHuanVerify :REJECT_： 55.9
haoHuanVerify :ACCEPT_： 69.2
【上周】===============================================================================================
haoHuanVerify :REJECT_： 55.2
haoHuanVerify :ACCEPT_： 75.5
===============================================================================================
节点名称： 流程前置操作  3.2
节点名称： IRR24 A数据  0.0
节点名称： IRR24 A特征  0.0
节点名称： IRR24 B数据  0.0
节点名称： IRR24 B特征  0.0
节点名称： IRR24 C数据  0.0
节点名称： IRR24 C特征  0.0
节点名称： IRR24/IRR/APR策略  0.0
节点名称： IRR/APR A数据  0.0
节点名称： IRR/APR A特征  0.0
节点名称： IRR_B数据  0.0
节点名称： APR_B数据  0.0
节点名称： IRR/APR B特征  0.0
节点名称： IRR C数据  0.0
节点名称： APR C数据  0.0
节点名称： IRR/APR C特征  0.0
节点名称： 资金路由特征  2.0
节点名称： 资金路由策略  2.0
节点名称： IRR_A_FL数据  7.6
节点名称： IRR_A_FL特征  6.5
节点名称： IRR_A_FL策略  0.6
节点名称： IRR_B_FL数据  16.1
节点名称： IRR_B_FL特征  5.9
节点名称： IRR_B_FL策略  1.6
节点名称： IRR_C_FL数据  4.9
节点名称： IRR_C_FL特征  4.2
节点名称： IRR_C_FL策略  1.7
节点名称： IRR_D_FL数据  14.6
节点名称： IRR_D_FL特征  6.1
节点名称： IRR_D_FL策略  1.7
节点名称： IRR_E_FL数据  3.7
节点名称： IRR_E_FL特征  4.4
节点名称： IRR_E_FL策略  1.7
节点名称： IRR额度数据节点  0.0
节点名称： IRR额度特征节点  0.0
节点名称： FL额度数据节点  0.7
节点名称： FL额度特征节点  1.7
节点名称： FL36额度数据节点  0.8
节点名称： FL36额度特征节点  1.3
节点名称： 流程前置操作  3.2
节点名称： IRR24 A数据  0.0
节点名称： IRR24 A特征  0.0
节点名称： IRR24 B数据  0.0
节点名称： IRR24 B特征  0.0
节点名称： IRR24 C数据  0.0
节点名称： IRR24 C特征  0.0
节点名称： IRR24/IRR/APR策略  0.0
节点名称： IRR/APR A数据  0.0
节点名称： IRR/APR A特征  0.0
节点名称： IRR_B数据  0.0
节点名称： APR_B数据  0.0
节点名称： IRR/APR B特征  0.0
节点名称： IRR C数据  0.0
节点名称： APR C数据  0.0
节点名称： IRR/APR C特征  0.0
节点名称： 资金路由特征  2.0
节点名称： 资金路由策略  2.0
节点名称： IRR_A_FL数据  7.5
节点名称： IRR_A_FL特征  6.5
节点名称： IRR_A_FL策略  0.6
节点名称： IRR_B_FL数据  16.2
节点名称： IRR_B_FL特征  6.2
节点名称： IRR_B_FL策略  1.6
节点名称： IRR_C_FL数据  4.8
节点名称： IRR_C_FL特征  4.4
节点名称： IRR_C_FL策略  1.7
节点名称： IRR_D_FL数据  15.4
节点名称： IRR_D_FL特征  6.1
节点名称： IRR_D_FL策略  1.6
节点名称： IRR_E_FL数据  3.2
节点名称： IRR_E_FL特征  4.3
节点名称： IRR_E_FL策略  1.7
节点名称： IRR额度数据节点  0.0
节点名称： IRR额度特征节点  0.0
节点名称： FL额度数据节点  0.7
节点名称： FL额度特征节点  1.7
节点名称： FL36额度数据节点  0.8
节点名称： FL36额度特征节点  1.3
节点名称： 流程前置操作  3.2
节点名称： IRR24 A数据  0.0
节点名称： IRR24 A特征  0.0
节点名称： IRR24 B数据  0.0
节点名称： IRR24 B特征  0.0
节点名称： IRR24 C数据  0.0
节点名称： IRR24 C特征  0.0
节点名称： IRR24/IRR/APR策略  0.0
节点名称： IRR/APR A数据  0.0
节点名称： IRR/APR A特征  0.0
节点名称： IRR_B数据  0.0
节点名称： APR_B数据  0.0
节点名称： IRR/APR B特征  0.0
节点名称： IRR C数据  0.0
节点名称： APR C数据  0.0
节点名称： IRR/APR C特征  0.0
节点名称： 资金路由特征  0.0
节点名称： 资金路由策略  0.0
节点名称： IRR_A_FL数据  7.7
节点名称： IRR_A_FL特征  6.5
节点名称： IRR_A_FL策略  0.7
节点名称： IRR_B_FL数据  16.0
节点名称： IRR_B_FL特征  5.6
节点名称： IRR_B_FL策略  1.6
节点名称： IRR_C_FL数据  5.1
节点名称： IRR_C_FL特征  4.1
节点名称： IRR_C_FL策略  1.7
节点名称： IRR_D_FL数据  13.7
节点名称： IRR_D_FL特征  6.1
节点名称： IRR_D_FL策略  1.7
节点名称： IRR_E_FL数据  4.2
节点名称： IRR_E_FL特征  4.5
节点名称： IRR_E_FL策略  1.8
节点名称： IRR额度数据节点  0.0
节点名称： IRR额度特征节点  0.0
节点名称： FL额度数据节点  0.0
节点名称： FL额度特征节点  0.0
节点名称： FL36额度数据节点  0.0
节点名称： FL36额度特征节点  0.0
===============================================================================================
节点名称： 流程前置操作  4.1
节点名称： IRR24 A数据  0.0
节点名称： IRR24 A特征  0.0
节点名称： IRR24 B数据  0.0
节点名称： IRR24 B特征  0.0
节点名称： IRR24 C数据  0.0
节点名称： IRR24 C特征  0.0
节点名称： IRR24/IRR/APR策略  0.0
节点名称： IRR/APR A数据  0.0
节点名称： IRR/APR A特征  0.0
节点名称： IRR_B数据  0.0
节点名称： APR_B数据  0.0
节点名称： IRR/APR B特征  0.0
节点名称： IRR C数据  0.0
节点名称： APR C数据  0.0
节点名称： IRR/APR C特征  0.0
节点名称： 资金路由特征  2.2
节点名称： 资金路由策略  2.1
节点名称： IRR_A_FL数据  7.9
节点名称： IRR_A_FL特征  6.9
节点名称： IRR_A_FL策略  0.7
节点名称： IRR_B_FL数据  17.7
节点名称： IRR_B_FL特征  6.8
节点名称： IRR_B_FL策略  1.8
节点名称： IRR_C_FL数据  4.4
节点名称： IRR_C_FL特征  5.0
节点名称： IRR_C_FL策略  1.9
节点名称： IRR_D_FL数据  12.3
节点名称： IRR_D_FL特征  5.6
节点名称： IRR_D_FL策略  1.5
节点名称： IRR_E_FL数据  3.0
节点名称： IRR_E_FL特征  4.1
节点名称： IRR_E_FL策略  1.5
节点名称： IRR额度数据节点  0.0
节点名称： IRR额度特征节点  0.0
节点名称： FL额度数据节点  0.3
节点名称： FL额度特征节点  1.9
节点名称： FL36额度数据节点  0.3
节点名称： FL36额度特征节点  1.0
节点名称： 流程前置操作  4.0
节点名称： IRR24 A数据  0.0
节点名称： IRR24 A特征  0.0
节点名称： IRR24 B数据  0.0
节点名称： IRR24 B特征  0.0
节点名称： IRR24 C数据  0.0
节点名称： IRR24 C特征  0.0
节点名称： IRR24/IRR/APR策略  0.0
节点名称： IRR/APR A数据  0.0
节点名称： IRR/APR A特征  0.0
节点名称： IRR_B数据  0.0
节点名称： APR_B数据  0.0
节点名称： IRR/APR B特征  0.0
节点名称： IRR C数据  0.0
节点名称： APR C数据  0.0
节点名称： IRR/APR C特征  0.0
节点名称： 资金路由特征  2.2
节点名称： 资金路由策略  2.1
节点名称： IRR_A_FL数据  8.3
节点名称： IRR_A_FL特征  6.9
节点名称： IRR_A_FL策略  0.7
节点名称： IRR_B_FL数据  18.0
节点名称： IRR_B_FL特征  7.0
节点名称： IRR_B_FL策略  1.8
节点名称： IRR_C_FL数据  4.7
节点名称： IRR_C_FL特征  5.0
节点名称： IRR_C_FL策略  1.9
节点名称： IRR_D_FL数据  12.8
节点名称： IRR_D_FL特征  5.0
节点名称： IRR_D_FL策略  1.3
节点名称： IRR_E_FL数据  3.1
节点名称： IRR_E_FL特征  3.6
节点名称： IRR_E_FL策略  1.3
节点名称： IRR额度数据节点  0.0
节点名称： IRR额度特征节点  0.0
节点名称： FL额度数据节点  0.3
节点名称： FL额度特征节点  1.9
节点名称： FL36额度数据节点  0.3
节点名称： FL36额度特征节点  1.0
节点名称： 流程前置操作  4.2
节点名称： IRR24 A数据  0.0
节点名称： IRR24 A特征  0.0
节点名称： IRR24 B数据  0.0
节点名称： IRR24 B特征  0.0
节点名称： IRR24 C数据  0.0
节点名称： IRR24 C特征  0.0
节点名称： IRR24/IRR/APR策略  0.0
节点名称： IRR/APR A数据  0.0
节点名称： IRR/APR A特征  0.0
节点名称： IRR_B数据  0.0
节点名称： APR_B数据  0.0
节点名称： IRR/APR B特征  0.0
节点名称： IRR C数据  0.0
节点名称： APR C数据  0.0
节点名称： IRR/APR C特征  0.0
节点名称： 资金路由特征  0.0
节点名称： 资金路由策略  0.0
节点名称： IRR_A_FL数据  7.5
节点名称： IRR_A_FL特征  6.9
节点名称： IRR_A_FL策略  0.7
节点名称： IRR_B_FL数据  17.5
节点名称： IRR_B_FL特征  6.7
节点名称： IRR_B_FL策略  1.8
节点名称： IRR_C_FL数据  4.1
节点名称： IRR_C_FL特征  5.0
节点名称： IRR_C_FL策略  1.9
节点名称： IRR_D_FL数据  12.0
节点名称： IRR_D_FL特征  6.0
节点名称： IRR_D_FL策略  1.6
节点名称： IRR_E_FL数据  3.0
节点名称： IRR_E_FL特征  4.4
节点名称： IRR_E_FL策略  1.7
节点名称： IRR额度数据节点  0.0
节点名称： IRR额度特征节点  0.0
节点名称： FL额度数据节点  0.0
节点名称： FL额度特征节点  0.0
节点名称： FL36额度数据节点  0.0
节点名称： FL36额度特征节点  0.0
===============================================================================================
java.io.FileNotFoundException: src\test\resources\haoHuanVerify_2023-06-01.xlsx (另一个程序正在使用此文件，进程无法访问。)
	at java.io.FileOutputStream.open0(Native Method)
	at java.io.FileOutputStream.open(FileOutputStream.java:270)
	at java.io.FileOutputStream.<init>(FileOutputStream.java:213)
	at java.io.FileOutputStream.<init>(FileOutputStream.java:101)
	at com.example.common.utils.ExcelUtil.exportFeedBack(ExcelUtil.java:190)
	at com.example.service.impl.StatisticsTimeServiceImplTest.testGetNodeCostByEventCode(StatisticsTimeServiceImplTest.java:55)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.springframework.test.context.junit4.statements.RunBeforeTestExecutionCallbacks.evaluate(RunBeforeTestExecutionCallbacks.java:74)
	at org.springframework.test.context.junit4.statements.RunAfterTestExecutionCallbacks.evaluate(RunAfterTestExecutionCallbacks.java:84)
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75)
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86)
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:251)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:190)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:33)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:221)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:54)
开始时间(本周): 2023-05-31 16:00:00 结束时间(本周): 2023-05-31 19:00:00 开始时间(上周): 2023-05-17 12:00:00 结束时间(上周): 2023-05-17 15:00:00
【本周】===============================================================================================
haoHuanLendAudit :REJECT_true： 49.2
haoHuanLendAudit :REJECT_null： 217.1
haoHuanLendAudit :REJECT_false： 62.5
haoHuanLendAudit :ACCEPT_true： 52.9
haoHuanLendAudit :ACCEPT_false： 87.7
【上周】===============================================================================================
haoHuanLendAudit :REJECT_true： 53.1
haoHuanLendAudit :ACCEPT_null： 236.6
haoHuanLendAudit :REJECT_false： 67.1
haoHuanLendAudit :ACCEPT_true： 55.5
haoHuanLendAudit :ACCEPT_false： 88.5
===============================================================================================
节点名称： 流程前置操作  2.0
节点名称： Base数据  17.9
节点名称： A数据（首贷）  1.3
节点名称： A特征（首贷）  9.3
节点名称： A策略（首贷）  1.1
节点名称： C数据（首贷）  0.4
节点名称： C特征（首贷）  1.7
节点名称： C策略（首贷）  1.0
节点名称： D数据（首贷）  0.0
节点名称： D特征（首贷）  0.0
节点名称： D策略（首贷）  0.0
节点名称： 流程前置操作  3.2
节点名称： Base数据  25.5
节点名称： A数据（复贷）  2.4
节点名称： A特征（复贷）  11.6
节点名称： A策略（复贷）  1.8
节点名称： B1数据（复贷）  0.5
节点名称： B2数据（复贷）  1.3
节点名称： B2特征（复贷）  8.7
节点名称： B1策略（复贷）  1.8
节点名称： B2策略（复贷）  2.5
节点名称： C数据（复贷）  16.7
节点名称： C特征（复贷）  4.1
节点名称： C策略（复贷）  2.5
节点名称： A数据（首贷）  1.9
节点名称： A特征（首贷）  11.0
节点名称： A策略（首贷）  1.3
节点名称： C数据（首贷）  0.5
节点名称： C特征（首贷）  2.4
节点名称： C策略（首贷）  1.3
节点名称： 流程前置操作  2.9
节点名称： Base数据  18.9
节点名称： A数据（复贷）  2.7
节点名称： A特征（复贷）  13.0
节点名称： A策略（复贷）  1.9
节点名称： B1数据（复贷）  0.5
节点名称： B2数据（复贷）  1.5
节点名称： B2特征（复贷）  9.6
节点名称： B1策略（复贷）  1.9
节点名称： B2策略（复贷）  2.5
节点名称： C数据（复贷）  7.4
节点名称： C特征（复贷）  4.5
节点名称： C策略（复贷）  2.6
节点名称： A数据（首贷）  1.3
节点名称： A特征（首贷）  9.3
节点名称： A策略（首贷）  1.1
节点名称： C数据（首贷）  0.4
节点名称： C特征（首贷）  1.7
节点名称： C策略（首贷）  1.0
节点名称： 流程前置操作  3.5
节点名称： Base数据  19.5
节点名称： A数据（复贷）  2.7
节点名称： A特征（复贷）  13.0
节点名称： A策略（复贷）  1.9
节点名称： B1数据（复贷）  0.5
节点名称： B2数据（复贷）  1.5
节点名称： B2特征（复贷）  9.6
节点名称： B1策略（复贷）  1.9
节点名称： B2策略（复贷）  2.5
节点名称： C数据（复贷）  7.4
节点名称： C特征（复贷）  4.5
节点名称： C策略（复贷）  2.6
节点名称： 流程前置操作  2.5
节点名称： Base数据  25.8
节点名称： A数据（首贷）  1.9
节点名称： A特征（首贷）  11.0
节点名称： A策略（首贷）  1.3
节点名称： C数据（首贷）  0.5
节点名称： C特征（首贷）  2.4
节点名称： C策略（首贷）  1.3
节点名称： D数据（首贷）  0.0
节点名称： D特征（首贷）  0.0
节点名称： D策略（首贷）  0.0
节点名称： 流程前置操作  3.8
节点名称： Base数据  25.2
节点名称： A数据（复贷）  2.4
节点名称： A特征（复贷）  11.6
节点名称： A策略（复贷）  1.8
节点名称： B1数据（复贷）  0.5
节点名称： B2数据（复贷）  1.3
节点名称： B2特征（复贷）  8.7
节点名称： B1策略（复贷）  1.8
节点名称： B2策略（复贷）  2.5
节点名称： C数据（复贷）  16.7
节点名称： C特征（复贷）  4.1
节点名称： C策略（复贷）  2.5
节点名称： 流程前置操作  3.1
节点名称： Base数据  22.5
节点名称： A数据（复贷）  2.5
节点名称： A特征（复贷）  12.3
节点名称： A策略（复贷）  1.8
节点名称： B1数据（复贷）  0.5
节点名称： B2数据（复贷）  1.4
节点名称： B2特征（复贷）  9.2
节点名称： B1策略（复贷）  1.8
节点名称： B2策略（复贷）  2.5
节点名称： C数据（复贷）  12.1
节点名称： C特征（复贷）  4.3
节点名称： C策略（复贷）  2.6
节点名称： A数据（首贷）  1.7
节点名称： A特征（首贷）  10.3
节点名称： A策略（首贷）  1.2
节点名称： C数据（首贷）  0.5
节点名称： C特征（首贷）  2.1
节点名称： C策略（首贷）  1.2
===============================================================================================
节点名称： 流程前置操作  2.2
节点名称： Base数据  21.3
节点名称： A数据（首贷）  1.9
节点名称： A特征（首贷）  10.1
节点名称： A策略（首贷）  1.2
节点名称： C数据（首贷）  0.5
节点名称： C特征（首贷）  2.1
节点名称： C策略（首贷）  1.2
节点名称： D数据（首贷）  0.0
节点名称： D特征（首贷）  0.0
节点名称： D策略（首贷）  0.0
节点名称： 流程前置操作  2.7
节点名称： Base数据  26.2
节点名称： A数据（复贷）  2.9
节点名称： A特征（复贷）  12.7
节点名称： A策略（复贷）  1.9
节点名称： B1数据（复贷）  0.6
节点名称： B2数据（复贷）  1.4
节点名称： B2特征（复贷）  9.7
节点名称： B1策略（复贷）  1.9
节点名称： B2策略（复贷）  2.6
节点名称： C数据（复贷）  14.7
节点名称： C特征（复贷）  4.6
节点名称： C策略（复贷）  2.7
节点名称： A数据（首贷）  2.2
节点名称： A特征（首贷）  12.0
节点名称： A策略（首贷）  1.4
节点名称： C数据（首贷）  0.6
节点名称： C特征（首贷）  2.4
节点名称： C策略（首贷）  1.4
节点名称： 流程前置操作  2.7
节点名称： Base数据  19.9
节点名称： A数据（复贷）  3.1
节点名称： A特征（复贷）  14.1
节点名称： A策略（复贷）  2.0
节点名称： B1数据（复贷）  0.6
节点名称： B2数据（复贷）  1.6
节点名称： B2特征（复贷）  10.5
节点名称： B1策略（复贷）  2.0
节点名称： B2策略（复贷）  2.7
节点名称： C数据（复贷）  9.0
节点名称： C特征（复贷）  4.9
节点名称： C策略（复贷）  2.8
节点名称： A数据（首贷）  1.9
节点名称： A特征（首贷）  10.1
节点名称： A策略（首贷）  1.2
节点名称： C数据（首贷）  0.5
节点名称： C特征（首贷）  2.1
节点名称： C策略（首贷）  1.2
节点名称： 流程前置操作  3.1
节点名称： Base数据  18.9
节点名称： A数据（复贷）  3.1
节点名称： A特征（复贷）  14.1
节点名称： A策略（复贷）  2.0
节点名称： B1数据（复贷）  0.6
节点名称： B2数据（复贷）  1.6
节点名称： B2特征（复贷）  10.5
节点名称： B1策略（复贷）  2.0
节点名称： B2策略（复贷）  2.7
节点名称： C数据（复贷）  9.0
节点名称： C特征（复贷）  4.9
节点名称： C策略（复贷）  2.8
节点名称： 流程前置操作  2.4
节点名称： Base数据  27.7
节点名称： A数据（首贷）  2.2
节点名称： A特征（首贷）  12.0
节点名称： A策略（首贷）  1.4
节点名称： C数据（首贷）  0.6
节点名称： C特征（首贷）  2.4
节点名称： C策略（首贷）  1.4
节点名称： D数据（首贷）  2.1
节点名称： D特征（首贷）  0.0
节点名称： D策略（首贷）  0.0
节点名称： 流程前置操作  2.9
节点名称： Base数据  24.7
节点名称： A数据（复贷）  2.9
节点名称： A特征（复贷）  12.7
节点名称： A策略（复贷）  1.9
节点名称： B1数据（复贷）  0.6
节点名称： B2数据（复贷）  1.4
节点名称： B2特征（复贷）  9.7
节点名称： B1策略（复贷）  1.9
节点名称： B2策略（复贷）  2.6
节点名称： C数据（复贷）  14.7
节点名称： C特征（复贷）  4.6
节点名称： C策略（复贷）  2.7
节点名称： 流程前置操作  2.7
节点名称： Base数据  23.3
节点名称： A数据（复贷）  3.0
节点名称： A特征（复贷）  13.4
节点名称： A策略（复贷）  2.0
节点名称： B1数据（复贷）  0.6
节点名称： B2数据（复贷）  1.5
节点名称： B2特征（复贷）  10.1
节点名称： B1策略（复贷）  2.0
节点名称： B2策略（复贷）  2.6
节点名称： C数据（复贷）  11.8
节点名称： C特征（复贷）  4.7
节点名称： C策略（复贷）  2.7
节点名称： A数据（首贷）  2.1
节点名称： A特征（首贷）  11.2
节点名称： A策略（首贷）  1.3
节点名称： C数据（首贷）  0.6
节点名称： C特征（首贷）  2.3
节点名称： C策略（首贷）  1.3
===============================================================================================
开始时间(本周): 2023-05-31 16:00:00 结束时间(本周): 2023-05-31 19:30:00 开始时间(上周): 2023-05-24 16:00:00 结束时间(上周): 2023-05-24 19:00:00
【本周】===============================================================================================
haoHuanAmountRepay :NONE_： 7.8
【上周】===============================================================================================
haoHuanAmountRepay :NONE_： 15.6
===============================================================================================
节点名称： 流程前置操作  3.7
节点名称： Base数据  0.4
节点名称： A数据  0.5
节点名称： A特征  1.8
节点名称： A策略  0.7
节点名称： B数据  0.0
节点名称： B特征  0.0
节点名称： B策略  0.0
节点名称： C_ONE特征  0.0
节点名称： C_ONE策略  0.0
节点名称： C_TWO特征  0.0
节点名称： C_TWO策略  0.0
===============================================================================================
节点名称： 流程前置操作  5.1
节点名称： Base数据  0.6
节点名称： A数据  1.9
节点名称： A特征  5.9
节点名称： A策略  1.4
节点名称： B数据  0.0
节点名称： B特征  0.0
节点名称： B策略  0.0
节点名称： C_ONE特征  0.0
节点名称： C_ONE策略  0.0
节点名称： C_TWO特征  0.0
节点名称： C_TWO策略  0.0
===============================================================================================
