package com.example.dto;

/**
 * json数据不一致储存对象
 */
public class DifferentDataDTO {

    /**
     * 字段名
     */
    private String fieldName;
    /**
     * 新字段值
     */
    private String newFieldValue;
    /**
     * 旧字段值
     */
    private String oldFieldValue;

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public String getNewFieldValue() {
        return newFieldValue;
    }

    public void setNewFieldValue(String newFieldValue) {
        this.newFieldValue = newFieldValue;
    }

    public String getOldFieldValue() {
        return oldFieldValue;
    }

    public void setOldFieldValue(String oldFieldValue) {
        this.oldFieldValue = oldFieldValue;
    }

}