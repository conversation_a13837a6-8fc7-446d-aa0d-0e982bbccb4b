package com.example.common.utils;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xddf.usermodel.chart.*;
import org.apache.poi.xssf.usermodel.*;
import org.openxmlformats.schemas.drawingml.x2006.chart.*;
import org.openxmlformats.schemas.drawingml.x2006.chart.STDLblPos.Enum;
import org.openxmlformats.schemas.drawingml.x2006.main.CTLineProperties;
import org.openxmlformats.schemas.drawingml.x2006.main.STPresetLineDashVal;

import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class ExcelUtil {

    public static XSSFSheet createSheet(XSSFWorkbook wb, String sheetName, int index) {
        XSSFSheet sheet = wb.createSheet();
        wb.setSheetName(index, sheetName);
        return sheet;
    }

    public static XSSFWorkbook getXSSFWorkbook(String[] title, Object[][] values, XSSFWorkbook wb, XSSFSheet sheet, String drawTitle) {
        if (wb == null) {
            throw new IllegalArgumentException("not init!");
        }
        // 在sheet中添加表头第0行,注意老版本poi对Excel的行数列数有限制short
        Row row = sheet.createRow(0);
        // 创建单元格，并设置值表头 设置表头居中
        XSSFCellStyle style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER); // 创建一个居中格式
        //设置自动列宽
        for (int i = 0; i < title.length; i++) {
            sheet.autoSizeColumn(i);
            sheet.setColumnWidth(i,sheet.getColumnWidth(i)*17/10);
        }

        Cell cell = null;
        //创建标题
        for (int i = 0; i < title.length; i++) {
            cell = row.createCell(i);
            cell.setCellValue(title[i]);
            cell.setCellStyle(style);
        }
        //创建内容
        for (int i = 0; i < values.length; i++) {
            row = sheet.createRow(i + 1);
            for (int j = 0; j < values[i].length; j++) {
                if(j == 0){
                    row.createCell(j).setCellValue((String) values[i][j]);
                }else{
                    row.createCell(j).setCellValue((Double) values[i][j]);
                }
            }
        }
        drawLine(sheet, drawTitle, values);
        return wb;
    }


    public static void drawLine(XSSFSheet sheet, String drawTitle, Object[][] values){
        //创建一个画布
        XSSFDrawing drawing = sheet.createDrawingPatriarch();
        //前四个默认0，[0,5]
        //默认宽度(14-8)*12
        XSSFClientAnchor anchor = drawing.createAnchor(0, 0, 0, 0, 3, 0, 17, 40);
        //创建一个chart对象
        XSSFChart chart = drawing.createChart(anchor);
        //标题
        chart.setTitleText(drawTitle);
        //标题覆盖
        chart.setTitleOverlay(false);

        //图例位置
        XDDFChartLegend legend = chart.getOrAddLegend();
        legend.setPosition(LegendPosition.TOP);

        //分类轴标(X轴),标题位置
        XDDFCategoryAxis bottomAxis = chart.createCategoryAxis(AxisPosition.BOTTOM);
        bottomAxis.setTitle("节点名称");

        //值(Y轴)轴,标题位置
        XDDFValueAxis leftAxis = chart.createValueAxis(AxisPosition.LEFT);
        leftAxis.setTitle("平均耗时");

        //LINE：折线图，
        XDDFLineChartData data = (XDDFLineChartData) chart.createData(ChartTypes.LINE, bottomAxis, leftAxis);
        // 设置为可变色
        data.setVaryColors(true);

        //CellRangeAddress(起始行号，终止行号， 起始列号，终止列号）
        //分类轴标(X轴)数据，单元格范围位置
        XDDFDataSource<String> nodes = XDDFDataSourcesFactory.fromStringCellRange(sheet,
                new CellRangeAddress(1, values.length , 0, 0));
        //数据1，单元格范围位置
        XDDFNumericalDataSource<Double> lastcCostTimes = XDDFDataSourcesFactory.fromNumericCellRange(sheet,
                new CellRangeAddress(1, values.length , 1, 1));

        //数据1，单元格范围位置
        XDDFNumericalDataSource<Double> costTimes = XDDFDataSourcesFactory.fromNumericCellRange(sheet,
                new CellRangeAddress(1, values.length , 2, 2));

        //图表加载数据，折线1
        XDDFLineChartData.Series series1 = (XDDFLineChartData.Series) data.addSeries(nodes, lastcCostTimes);
        //折线图例标题
        series1.setTitle("上周", null);
        //直线
        series1.setSmooth(false);
        //设置标记大小
        series1.setMarkerSize((short) 6);
        //设置标记样式，星星
        series1.setMarkerStyle(MarkerStyle.STAR);

        //图表加载数据，折线2
        XDDFLineChartData.Series series2 = (XDDFLineChartData.Series) data.addSeries(nodes, costTimes);
        //折线图例标题
        series2.setTitle("本周", null);
        //曲线
        series2.setSmooth(true);
        //设置标记大小
        series2.setMarkerSize((short) 6);
        //设置标记样式，正方形
        series2.setMarkerStyle(MarkerStyle.SQUARE);
        // 添加节点标识
        changeLabel(chart);

        //获取Y轴 图表的基本配置都在这个对象里面里面
        CTValAx catAy = chart.getCTChart().getPlotArea().getValAxArray(0);
        // 设置图表背后的网格线
        CTLineProperties ctLine = catAy.addNewMajorGridlines().addNewSpPr().addNewLn();
        ctLine.addNewPrstDash().setVal(STPresetLineDashVal.DASH);

        //获取X轴 图表的基本配置都在这个对象里面里面
        CTCatAx catAx = chart.getCTChart().getPlotArea().getCatAxArray(0);
        //设置标签位置为最下
        CTTickLblPos ctTickLblPos = CTTickLblPos.Factory.newInstance();
        ctTickLblPos.setVal(STTickLblPos.LOW);
        catAx.setTickLblPos(ctTickLblPos);

        //绘制
        chart.plot(data);
    }
    
    private static void changeLabel(XSSFChart chart){
        CTPlotArea plotArea = chart.getCTChart().getPlotArea();
        List<CTLineChart> lineChartList = plotArea.getLineChartList();
        for(CTLineChart ctLineChart : lineChartList){
            List<CTLineSer> serList = ctLineChart.getSerList();
            int i = 0;
            for(CTLineSer ctLineSer : serList){
                // Add data labels
                if (!ctLineSer.isSetDLbls()) {
                    ctLineSer.addNewDLbls();
                }
                ctLineSer.getDLbls().addNewShowVal().setVal(true);
                ctLineSer.getDLbls().addNewShowSerName().setVal(false);
                ctLineSer.getDLbls().addNewShowCatName().setVal(false);
                ctLineSer.getDLbls().addNewShowPercent().setVal(false);
                ctLineSer.getDLbls().addNewShowLegendKey().setVal(false);
                ctLineSer.getDLbls().addNewNumFmt().setFormatCode("0,00");
                CTDLblPos ctdLblPos = ctLineSer.getDLbls().addNewDLblPos();
                if(0 == i){
                    ctdLblPos.setVal(Enum.forInt(9));
                    i++;
                } else{
                    ctdLblPos.setVal(Enum.forInt(2));
                }
            }
        }
    }

    public static void exportFeedBack(String fileName, Map<String, Object[][]> excelDatas, String[] title) {
        XSSFWorkbook wb = new XSSFWorkbook();

        Set<Map.Entry<String, Object[][]>> entries = excelDatas.entrySet();
        int index = 0;
        for(Map.Entry<String, Object[][]> entry: entries){
            XSSFSheet sheet = ExcelUtil.createSheet(wb, entry.getKey(), index);
            ExcelUtil.getXSSFWorkbook(title, entry.getValue(), wb, sheet, entry.getKey());
            index++;
        }

        //将文件存到指定位置
        try {
            OutputStream os = new FileOutputStream(fileName);
            wb.write(os);
            os.flush();
            os.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

