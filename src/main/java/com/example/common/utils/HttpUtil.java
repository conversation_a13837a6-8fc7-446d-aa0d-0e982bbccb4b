package com.example.common.utils;

import org.apache.http.*;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpRequestRetryHandler;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.*;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.InputStreamEntity;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by chengliwei on 2018/8/1.
 */
public class HttpUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(HttpUtil.class);

    private static HttpUtil httpUtil = null;
    private static CloseableHttpClient httpClient = null;

    private HttpUtil() {
    }

    public static HttpUtil getHttpClient() {
        if (httpUtil == null) {
            synchronized (HttpUtil.class) {
                if (httpUtil == null) {
                    httpUtil = new HttpUtil();
                }
            }
        }
        try {
            httpClient = newHttpClient();
        } catch (KeyStoreException e) {
            e.printStackTrace();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (KeyManagementException e) {
            e.printStackTrace();
        }
        return httpUtil;
    }

    private static CloseableHttpClient newHttpClient()
            throws KeyStoreException, NoSuchAlgorithmException, KeyManagementException {
//        HttpClients.createDefault();
        ConnectionSocketFactory factory = PlainConnectionSocketFactory.getSocketFactory();
        SSLContext sslContext = new SSLContextBuilder()
                .loadTrustMaterial(null, new TrustStrategy() {
                    //信任所有
                    public boolean isTrusted(X509Certificate[] chain, String authType)
                            throws CertificateException {
                        return true;
                    }
                }).build();
        Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory>create()
                .register("http", factory)
                .register("https", new SSLConnectionSocketFactory(sslContext))
                .build();
        PoolingHttpClientConnectionManager manager = new PoolingHttpClientConnectionManager(
                registry);
        manager.setMaxTotal(1 << 4);
        manager.setDefaultMaxPerRoute(1 << 4);
        HttpRequestRetryHandler retryHandler = (e, i, httpContext) -> {
            if (e instanceof NoHttpResponseException) {
                return true;
            }
            if (i >= 3) {
                return false;
            }
            HttpClientContext context = HttpClientContext.adapt(httpContext);
            HttpRequest request = context.getRequest();
            if (!(request instanceof HttpEntityEnclosingRequest)) {
                return true;
            }
            return false;
        };

        return HttpClients.custom()
                .setConnectionManager(manager)
                .setRetryHandler(retryHandler)
                .build();
    }

    public String get(String url) {
        HttpGet get = new HttpGet(url);
        return execute(get);
    }

    public InputStream getFileStream(String url) throws IOException {
        HttpGet get = new HttpGet(url);
        CloseableHttpResponse response = httpClient.execute(get);
        return response.getEntity().getContent();
    }

    public String post(String url) {
        HttpPost post = new HttpPost(url);
        return execute(post);
    }

    public String post(String url, Map<String, String> params) {
        HttpPost post = new HttpPost(url);
        List<NameValuePair> paramValuePairs = params.entrySet().stream()
                .map(param -> new BasicNameValuePair(param.getKey(), param.getValue()))
                .collect(Collectors.toList());
        try {
            HttpEntity entity = new UrlEncodedFormEntity(paramValuePairs, "UTF-8");
            post.setEntity(entity);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return execute(post);
    }

    public String post(String url, Map<String, String> headers, String body) {
        HttpPost post = new HttpPost(url);
        for (Map.Entry<String, String> header : headers.entrySet()) {
            post.addHeader(header.getKey(), header.getValue());
        }
        post.setEntity(new StringEntity(body, Charset.forName("UTF-8")));
        return execute(post);
    }

    public String put(String url, InputStream inputStream, ContentType contentType) {
        HttpPut put = new HttpPut(url);
        HttpEntity entity = new InputStreamEntity(inputStream, contentType);
        put.setEntity(entity);
        return execute(put);
    }

    public String delete(String url) {
        HttpDelete delete = new HttpDelete(url);
        return execute(delete);
    }

    private String execute(HttpUriRequest request) {
//        LOGGER.info("http request: [{}]", request);
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            /** 返回状态码不正确 **/
            if(response.getStatusLine().getStatusCode() != 200){
                return null;
            }
            HttpEntity entity = response.getEntity();
            return EntityUtils.toString(entity);
        } catch (ClientProtocolException e) {
            LOGGER.error(e.getMessage());
        } catch (IOException e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }
}
