package com.example.common.utils;

import com.alibaba.fastjson.JSON;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class AlertUtils {

    @Value("${alert.service.url}")
    private String alertServiceUrl;

    public QueryResult queryInfluxdb(String database, Query sql){

        String urlBuilder = alertServiceUrl +
                String.format("/alert/%s/query?command=", database) +
                sql.getCommandWithUrlEncoded();
        String response = HttpUtil.getHttpClient().get(urlBuilder);
        return JSON.parseObject(response, QueryResult.class);
    }
}
