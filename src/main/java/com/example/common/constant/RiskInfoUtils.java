package com.example.common.constant;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.csv.CsvData;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvRow;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.util.CharsetUtil;
import com.alibaba.fastjson.JSONObject;

import java.util.*;

public class RiskInfoUtils {
    // 上周日期
    public static String lastWeek = "2025-02-19";
    // 本周日期
    public static String thisWeek = "2025-02-26";
    // 老事件开始时间
    public static String oldStartTime = " 12:00:00";
    // 老事件结束时间
    public static String oldEndTime = " 15:00:00";
    // 新事件开始时间
    public static String newStartTime = " 08:30:00";
    // 新事件结束时间
    public static String newEndTime = " 11:30:00";

    // CombApiLendAudit 上周开始日期
    public static String combLastWeekStart = "2025-02-19";
    // CombApiLendAudit 上周结束日期
    public static String combLastWeekEnd = "2025-02-20";
    // CombApiLendAudit 本周开始日期
    public static String combThisWeekStart = "2025-02-26";
    // CombApiLendAudit 本周结束日期
    public static String combThisWeekEnd = "2025-02-27";

    public static Map<String, Map<String, String>> eventCodeTimeRanges = new LinkedHashMap<String, Map<String, String>>(){{
        put("haoHuanVerify", new HashMap<String, String>(){{
            put("lastStartTime", lastWeek + oldStartTime); // 上周的时间
            put("lastEndTime", lastWeek + oldEndTime);  // 上周的时间
            put("startTime", thisWeek + oldStartTime);
            put("endTime", thisWeek + oldEndTime);
        }});
        put("haoHuanLendAudit", new HashMap<String, String>(){{
            put("lastStartTime", lastWeek + oldStartTime); // 上周的时间
            put("lastEndTime", lastWeek + oldEndTime);  // 上周的时间
            put("startTime", thisWeek + oldStartTime);
            put("endTime", thisWeek + oldEndTime);
        }});
        put("haoHuanLendAuditReloan", new HashMap<String, String>(){{
            put("lastStartTime", lastWeek + oldStartTime); // 上周的时间
            put("lastEndTime", lastWeek + oldEndTime);  // 上周的时间
            put("startTime", thisWeek + oldStartTime);
            put("endTime", thisWeek + oldEndTime);
        }});
        put("haoHuanAmountRepay", new HashMap<String, String>(){{
            put("lastStartTime", lastWeek + oldStartTime); // 上周的时间
            put("lastEndTime", lastWeek + oldEndTime);  // 上周的时间
            put("startTime", thisWeek + oldStartTime);
            put("endTime", thisWeek + oldEndTime);
        }});
        put("ApiVerify", new HashMap<String, String>(){{
            put("lastStartTime", lastWeek + newStartTime); // 上周的时间
            put("lastEndTime", lastWeek + newEndTime);  // 上周的时间
            put("startTime", thisWeek + newStartTime);
            put("endTime", thisWeek + newEndTime);
        }});
        put("ApiLendAudit", new HashMap<String, String>(){{
            put("lastStartTime", lastWeek + newStartTime); // 上周的时间
            put("lastEndTime", lastWeek + newEndTime);  // 上周的时间
            put("startTime", thisWeek + newStartTime);
            put("endTime", thisWeek + newEndTime);
        }});
        put("CombApiLendAudit", new HashMap<String, String>(){{
            put("lastStartTime", combLastWeekStart + " 00:00:00"); // 上周的时间
            put("lastEndTime", combLastWeekEnd + " 00:00:00");  // 上周的时间
            put("startTime", combThisWeekStart + " 00:00:00");
            put("endTime", combThisWeekEnd + " 00:00:00");// 上周日期
        }});
    }};

    public static Map<String, Map<String, Map<String, List<String>>>> eventCodes = new LinkedHashMap<>();

    static {
        Map<String, List<String>> haoHuanVerifyNodeForNameMap = new LinkedHashMap<>();
        haoHuanVerifyNodeForNameMap.put("startNode", Arrays.asList("startNode"));
        haoHuanVerifyNodeForNameMap.put("hhUserBasicData", Arrays.asList("hhUserBasicData"));
        haoHuanVerifyNodeForNameMap.put("hhUserLevel", Arrays.asList("hhUserLevel"));
        haoHuanVerifyNodeForNameMap.put("hhDataVo", Arrays.asList("hhDataVo"));
        haoHuanVerifyNodeForNameMap.put("whiteListCheckTask", Arrays.asList("whiteListCheckTask"));
        haoHuanVerifyNodeForNameMap.put("IRR_DS_FL_var", Arrays.asList("IRR_DS_FL_var"));
        haoHuanVerifyNodeForNameMap.put("IRR_PA_FL_var", Arrays.asList("IRR_PA_FL_var"));
        haoHuanVerifyNodeForNameMap.put("IRR_D_FL_var", Arrays.asList("IRR_D_FL_var"));
        haoHuanVerifyNodeForNameMap.put("attributionData", Arrays.asList("attributionData"));
        haoHuanVerifyNodeForNameMap.put("IRR_A_FL_var", Arrays.asList("IRR_A_FL_var"));
        haoHuanVerifyNodeForNameMap.put("IRR_PD_FL_var", Arrays.asList("IRR_PD_FL_var"));
        haoHuanVerifyNodeForNameMap.put("IRR_PA_FL_AF_var", Arrays.asList("IRR_PA_FL_AF_var"));
        haoHuanVerifyNodeForNameMap.put("IRR_DS_FL", Arrays.asList("IRR_DS_FL"));
        haoHuanVerifyNodeForNameMap.put("IRR_PA_FL", Arrays.asList("IRR_PA_FL"));
        haoHuanVerifyNodeForNameMap.put("IRR_D_FL", Arrays.asList("IRR_D_FL"));
        haoHuanVerifyNodeForNameMap.put("FL_IRR_hhVarStepC", Arrays.asList("FL_IRR_hhVarStepC"));
        haoHuanVerifyNodeForNameMap.put("attributionFeature", Arrays.asList("attributionFeature"));
        haoHuanVerifyNodeForNameMap.put("IRR_PD_FL", Arrays.asList("IRR_PD_FL"));
        haoHuanVerifyNodeForNameMap.put("IRR_PA_FL_AF", Arrays.asList("IRR_PA_FL_AF"));
        haoHuanVerifyNodeForNameMap.put("IRR_A_FL", Arrays.asList("IRR_A_FL"));
        haoHuanVerifyNodeForNameMap.put("FL_IRR_hhStrategyStepPA", Arrays.asList("FL_IRR_hhStrategyStepPA"));
        haoHuanVerifyNodeForNameMap.put("attributionStrategyTask", Arrays.asList("attributionStrategyTask"));
        haoHuanVerifyNodeForNameMap.put("FL36_IRR_hhLoanAmountVar", Arrays.asList("FL36_IRR_hhLoanAmountVar"));
        haoHuanVerifyNodeForNameMap.put("hhVerifyBaseinfo", Arrays.asList("hhVerifyBaseinfo"));
        haoHuanVerifyNodeForNameMap.put("lastVerifyResultVarWrite", Arrays.asList("lastVerifyResultVarWrite"));
        haoHuanVerifyNodeForNameMap.put("hhResultTransform", Arrays.asList("hhResultTransform"));
        haoHuanVerifyNodeForNameMap.put("Irr36VerifyLoanAmountWrite", Arrays.asList("Irr36VerifyLoanAmountWrite"));
        haoHuanVerifyNodeForNameMap.put("Irr24VerifyLoanAmountWrite", Arrays.asList("Irr24VerifyLoanAmountWrite"));
        haoHuanVerifyNodeForNameMap.put("IRR_F_FL_var", Arrays.asList("IRR_F_FL_var"));
        haoHuanVerifyNodeForNameMap.put("IRR_F_FL", Arrays.asList("IRR_F_FL"));
        haoHuanVerifyNodeForNameMap.put("IRR_E_FL_var", Arrays.asList("IRR_E_FL_var"));
        haoHuanVerifyNodeForNameMap.put("IRR_E_FL", Arrays.asList("IRR_E_FL"));
        haoHuanVerifyNodeForNameMap.put("IRR_C_FL_var", Arrays.asList("IRR_C_FL_var"));
        haoHuanVerifyNodeForNameMap.put("IRR_C_FL", Arrays.asList("IRR_C_FL"));
        haoHuanVerifyNodeForNameMap.put("IRR_B_FL_var", Arrays.asList("IRR_B_FL_var"));
        haoHuanVerifyNodeForNameMap.put("IRR_B_FL", Arrays.asList("IRR_B_FL"));
        haoHuanVerifyNodeForNameMap.put("IRR_AMOUNT_FL_var", Arrays.asList("IRR_AMOUNT_FL_var"));
        haoHuanVerifyNodeForNameMap.put("IRR_AMOUNT_FL_36_var", Arrays.asList("IRR_AMOUNT_FL_36_var"));
        haoHuanVerifyNodeForNameMap.put("IRR_AMOUNT_FL_36", Arrays.asList("IRR_AMOUNT_FL_36"));
        haoHuanVerifyNodeForNameMap.put("IRR_AMOUNT_FL", Arrays.asList("IRR_AMOUNT_FL"));
        haoHuanVerifyNodeForNameMap.put("FL_irr36ForIrrAccept36WriteBack", Arrays.asList("FL_irr36ForIrrAccept36WriteBack"));
        haoHuanVerifyNodeForNameMap.put("FL_IrrAccept36WriteBack", Arrays.asList("FL_IrrAccept36WriteBack"));
        haoHuanVerifyNodeForNameMap.put("FL_ForIrrAccept36WriteBack", Arrays.asList("FL_ForIrrAccept36WriteBack"));
        haoHuanVerifyNodeForNameMap.put("FL_ForIrr24AcceptWriteBack", Arrays.asList("FL_ForIrr24AcceptWriteBack"));

        // 放款复贷
        Map<String, List<String>> haoHuanLendAuditNoNewLoanNodeForNameMap = new LinkedHashMap<>();
        haoHuanLendAuditNoNewLoanNodeForNameMap.put("流程前置操作", Arrays.asList("startNode"));
        haoHuanLendAuditNoNewLoanNodeForNameMap.put("A变量", Arrays.asList("HH_Lend_A_var"));
        haoHuanLendAuditNoNewLoanNodeForNameMap.put("A策略", Arrays.asList("HH_Lend_A"));
        haoHuanLendAuditNoNewLoanNodeForNameMap.put("B变量", Arrays.asList("HH_Lend_B_var"));
        haoHuanLendAuditNoNewLoanNodeForNameMap.put("B策略", Arrays.asList("HH_Lend_B"));
        haoHuanLendAuditNoNewLoanNodeForNameMap.put("C变量", Arrays.asList("HH_Lend_C_var"));
        haoHuanLendAuditNoNewLoanNodeForNameMap.put("C策略", Arrays.asList("HH_Lend_C"));
        haoHuanLendAuditNoNewLoanNodeForNameMap.put("PuDaoVerifyData", Arrays.asList("PuDaoVerifyData"));
        haoHuanLendAuditNoNewLoanNodeForNameMap.put("verifyEndNotifyTaskService", Arrays.asList("verifyEndNotifyTaskService"));

        // 放款首贷
        Map<String, List<String>> haoHuanLendAuditIsNewLoanNodeForNameMap = new LinkedHashMap<>();
        haoHuanLendAuditIsNewLoanNodeForNameMap.put("流程前置操作", Arrays.asList("startNode"));
        haoHuanLendAuditIsNewLoanNodeForNameMap.put("A变量（36期）", Arrays.asList("HH_Lend_A_SD_36_var"));
        haoHuanLendAuditIsNewLoanNodeForNameMap.put("A变量（AF）", Arrays.asList("HH_Lend_A_SD_AF_var"));
        haoHuanLendAuditIsNewLoanNodeForNameMap.put("A变量（24期）", Arrays.asList("HH_Lend_A_SD_24_var"));
        haoHuanLendAuditIsNewLoanNodeForNameMap.put("A策略（AF）", Arrays.asList("HH_Lend_A_SD_AF"));
        haoHuanLendAuditIsNewLoanNodeForNameMap.put("A策略（IVR_AF）", Arrays.asList("HH_Lend_A_SD_IVR_AF"));
        haoHuanLendAuditIsNewLoanNodeForNameMap.put("A策略（36期）", Arrays.asList("HH_Lend_A_SD_36"));
        haoHuanLendAuditIsNewLoanNodeForNameMap.put("A策略（24期）", Arrays.asList("HH_Lend_A_SD_24"));
        haoHuanLendAuditIsNewLoanNodeForNameMap.put("放款结果任务", Arrays.asList("hhLendResultTask"));
        haoHuanLendAuditIsNewLoanNodeForNameMap.put("分流检查任务", Arrays.asList("diversionCheckTask"));
        haoHuanLendAuditIsNewLoanNodeForNameMap.put("断直连3数据节点", Arrays.asList("hhLendLastPointEventVerifyData"));
        haoHuanLendAuditIsNewLoanNodeForNameMap.put("审核结束通知任务", Arrays.asList("verifyEndNotifyTaskService"));

        // 还款事件节点
        Map<String, List<String>> haoHuanAmountRepayNodeForNameMap = new LinkedHashMap<>();
        haoHuanAmountRepayNodeForNameMap.put("流程前置操作", Arrays.asList("startNode"));
        haoHuanAmountRepayNodeForNameMap.put("Base数据", Arrays.asList("haoHuanRepayBaseData"));
        haoHuanAmountRepayNodeForNameMap.put("A数据", Arrays.asList("hhAmountRepayP1Data"));
        haoHuanAmountRepayNodeForNameMap.put("A特征", Arrays.asList("hhAmountRepayP1Feature"));
        haoHuanAmountRepayNodeForNameMap.put("A策略", Arrays.asList("hhAmountRepayP1Strategy"));
        haoHuanAmountRepayNodeForNameMap.put("B数据", Arrays.asList("hhAmountRepayP2Data"));
        haoHuanAmountRepayNodeForNameMap.put("B特征", Arrays.asList("hhAmountRepayP2Feature"));
        haoHuanAmountRepayNodeForNameMap.put("B策略", Arrays.asList("hhAmountRepayP2Strategy"));
        haoHuanAmountRepayNodeForNameMap.put("C_ONE特征", Arrays.asList("hhAmountRepayP3FeatureOne"));
        haoHuanAmountRepayNodeForNameMap.put("C_ONE策略", Arrays.asList("hhAmountRepayP3StrategyOne"));
        haoHuanAmountRepayNodeForNameMap.put("C_TWO特征", Arrays.asList("hhAmountRepayP3FeatureTwo"));
        haoHuanAmountRepayNodeForNameMap.put("C_TWO策略", Arrays.asList("hhAmountRepayP3StrategyTwo"));

        // 度小满进件事件节点
        Map<String, List<String>> haoHuanApiVerifyNodeForNameMap = new LinkedHashMap<>();
        haoHuanApiVerifyNodeForNameMap.put("流程前置操作", Arrays.asList("startNode"));
        haoHuanApiVerifyNodeForNameMap.put("apiVerifyCreditData", Arrays.asList("apiVerifyCreditData"));
        haoHuanApiVerifyNodeForNameMap.put("apiVerifyCreditStrategyPA_var", Arrays.asList("apiVerifyCreditStrategyPA_var"));
        haoHuanApiVerifyNodeForNameMap.put("apiVerifyCreditStrategyA_var", Arrays.asList("apiVerifyCreditStrategyA_var"));
        haoHuanApiVerifyNodeForNameMap.put("apiVerifyCreditStrategyB_var", Arrays.asList("apiVerifyCreditStrategyB_var"));
        haoHuanApiVerifyNodeForNameMap.put("apiVerifyCreditStrategyC_var", Arrays.asList("apiVerifyCreditStrategyC_var"));
        haoHuanApiVerifyNodeForNameMap.put("apiVerifyCreditStrategyPA", Arrays.asList("apiVerifyCreditStrategyPA"));
        haoHuanApiVerifyNodeForNameMap.put("apiVerifyCreditStrategyA", Arrays.asList("apiVerifyCreditStrategyA"));
        haoHuanApiVerifyNodeForNameMap.put("apiVerifyCreditStrategyB", Arrays.asList("apiVerifyCreditStrategyB"));
        haoHuanApiVerifyNodeForNameMap.put("apiVerifyCreditStrategyC", Arrays.asList("apiVerifyCreditStrategyC"));
        haoHuanApiVerifyNodeForNameMap.put("apiVerifyAmount_A_var", Arrays.asList("apiVerifyAmount_A_var"));
        haoHuanApiVerifyNodeForNameMap.put("apiVerifyAmount_A", Arrays.asList("apiVerifyAmount_A"));
        haoHuanApiVerifyNodeForNameMap.put("apiVerifyLoanAmountWrite", Arrays.asList("apiVerifyLoanAmountWrite"));
        haoHuanApiVerifyNodeForNameMap.put("verifyEndNotifyTaskService", Arrays.asList("verifyEndNotifyTaskService"));
        haoHuanApiVerifyNodeForNameMap.put("apiVerifyWriteBackRejectTaskService", Arrays.asList("apiVerifyWriteBackRejectTaskService"));

        // 度小满放款审核事件节点
        Map<String, List<String>> haoHuanApiLendAuditNodeForNameMap = new LinkedHashMap<>();
        haoHuanApiLendAuditNodeForNameMap.put("流程前置操作", Arrays.asList("startNode"));
        haoHuanApiLendAuditNodeForNameMap.put("apiLendAuditData", Arrays.asList("apiLendAuditData"));
        haoHuanApiLendAuditNodeForNameMap.put("apiStrategyASD_var", Arrays.asList("apiStrategyASD_var"));
        haoHuanApiLendAuditNodeForNameMap.put("apiStrategyBSD_var", Arrays.asList("apiStrategyBSD_var"));
        haoHuanApiLendAuditNodeForNameMap.put("apiStrategyCSD_var", Arrays.asList("apiStrategyCSD_var"));
        haoHuanApiLendAuditNodeForNameMap.put("apiStrategyASD", Arrays.asList("apiStrategyASD"));
        haoHuanApiLendAuditNodeForNameMap.put("apiStrategyBSD", Arrays.asList("apiStrategyBSD"));
        haoHuanApiLendAuditNodeForNameMap.put("apiStrategyCSD", Arrays.asList("apiStrategyCSD"));
        haoHuanApiLendAuditNodeForNameMap.put("apiStrategyLendA_var", Arrays.asList("apiStrategyLendA_var"));
        haoHuanApiLendAuditNodeForNameMap.put("apiStrategyLendB_var", Arrays.asList("apiStrategyLendB_var"));
        haoHuanApiLendAuditNodeForNameMap.put("apiStrategyLendA", Arrays.asList("apiStrategyLendA"));
        haoHuanApiLendAuditNodeForNameMap.put("apiStrategyLendB", Arrays.asList("apiStrategyLendB"));
        haoHuanApiLendAuditNodeForNameMap.put("verifyEndNotifyTaskService", Arrays.asList("verifyEndNotifyTaskService"));
        haoHuanApiLendAuditNodeForNameMap.put("apiLendIsNewLoanTask", Arrays.asList("apiLendIsNewLoanTask"));

        // api放款兼容事件节点
        Map<String, List<String>> haoHuanCombApiLendAuditNodeForNameMap = new LinkedHashMap<>();
        haoHuanCombApiLendAuditNodeForNameMap.put("流程前置操作", Arrays.asList("startNode"));
        haoHuanCombApiLendAuditNodeForNameMap.put("apiLendAuditData", Arrays.asList("apiLendAuditData"));
        haoHuanCombApiLendAuditNodeForNameMap.put("apiCombineStrategyASD_var", Arrays.asList("apiCombineStrategyASD_var"));
        haoHuanCombApiLendAuditNodeForNameMap.put("apiCombineStrategyBSD_var", Arrays.asList("apiCombineStrategyBSD_var"));
        haoHuanCombApiLendAuditNodeForNameMap.put("apiCombineStrategyCSD_var", Arrays.asList("apiCombineStrategyCSD_var"));
        haoHuanCombApiLendAuditNodeForNameMap.put("apiCombineStrategyASD", Arrays.asList("apiCombineStrategyASD"));
        haoHuanCombApiLendAuditNodeForNameMap.put("apiCombineStrategyBSD", Arrays.asList("apiCombineStrategyBSD"));
        haoHuanCombApiLendAuditNodeForNameMap.put("apiCombineStrategyCSD", Arrays.asList("apiCombineStrategyCSD"));
        haoHuanCombApiLendAuditNodeForNameMap.put("apiCombineStrategyLendA_var", Arrays.asList("apiCombineStrategyLendA_var"));
        haoHuanCombApiLendAuditNodeForNameMap.put("apiCombineStrategyLendB_var", Arrays.asList("apiCombineStrategyLendB_var"));
        haoHuanCombApiLendAuditNodeForNameMap.put("apiCombineStrategyLendA", Arrays.asList("apiCombineStrategyLendA"));
        haoHuanCombApiLendAuditNodeForNameMap.put("apiCombineStrategyLendB", Arrays.asList("apiCombineStrategyLendB"));
        haoHuanCombApiLendAuditNodeForNameMap.put("verifyEndNotifyTaskService", Arrays.asList("verifyEndNotifyTaskService"));

        // -----------------------------------------------------------------------------------
        Map<String, Map<String, List<String>>> haoHuanVerifyDim= new HashMap<>();
        haoHuanVerifyDim.put("haoHuanVerify", haoHuanVerifyNodeForNameMap);
        haoHuanVerifyDim.put("REJECT_haoHuanVerify", haoHuanVerifyNodeForNameMap);
        haoHuanVerifyDim.put("ACCEPT_haoHuanVerify", haoHuanVerifyNodeForNameMap);

        Map<String, Map<String, List<String>>> haoHuanLendAuditDim= new HashMap<>();
        haoHuanLendAuditDim.put("REJECT_haoHuanLendAudit", haoHuanLendAuditIsNewLoanNodeForNameMap);
        haoHuanLendAuditDim.put("ACCEPT_haoHuanLendAudit", haoHuanLendAuditIsNewLoanNodeForNameMap);
        Map<String, Map<String, List<String>>> haoHuanLendAuditDimReloan= new HashMap<>();
        haoHuanLendAuditDimReloan.put("REJECT_haoHuanLendAuditReloan", haoHuanLendAuditNoNewLoanNodeForNameMap);
        haoHuanLendAuditDimReloan.put("ACCEPT_haoHuanLendAuditReloan", haoHuanLendAuditNoNewLoanNodeForNameMap);

        Map<String, Map<String, List<String>>> haoHuanAmountRepayDim= new HashMap<>();
        haoHuanAmountRepayDim.put("haoHuanAmountRepay", haoHuanAmountRepayNodeForNameMap);

        Map<String, Map<String, List<String>>> ApiVerifyDim= new HashMap<>();
        ApiVerifyDim.put("ApiVerify", haoHuanApiVerifyNodeForNameMap);
        ApiVerifyDim.put("REJECT_ApiVerify", haoHuanApiVerifyNodeForNameMap);
        ApiVerifyDim.put("ACCEPT_ApiVerify", haoHuanApiVerifyNodeForNameMap);

        Map<String, Map<String, List<String>>> ApiLendAuditDim= new HashMap<>();
        ApiLendAuditDim.put("ApiLendAudit", haoHuanApiLendAuditNodeForNameMap);
        ApiLendAuditDim.put("REJECT_ApiLendAudit", haoHuanApiLendAuditNodeForNameMap);
        ApiLendAuditDim.put("ACCEPT_ApiLendAudit", haoHuanApiLendAuditNodeForNameMap);

        Map<String, Map<String, List<String>>> CombApiLendAuditDim= new HashMap<>();
        CombApiLendAuditDim.put("CombApiLendAudit", haoHuanCombApiLendAuditNodeForNameMap);
        CombApiLendAuditDim.put("REJECT_CombApiLendAudit", haoHuanCombApiLendAuditNodeForNameMap);
        CombApiLendAuditDim.put("ACCEPT_CombApiLendAudit", haoHuanCombApiLendAuditNodeForNameMap);

        // -----------------------------------------------------------------------------------

        eventCodes.put("haoHuanVerify", haoHuanVerifyDim);
        eventCodes.put("haoHuanLendAudit", haoHuanLendAuditDim);
        eventCodes.put("haoHuanLendAuditReloan", haoHuanLendAuditDimReloan);
        eventCodes.put("haoHuanAmountRepay", haoHuanAmountRepayDim);
        eventCodes.put("ApiVerify", ApiVerifyDim);
        eventCodes.put("ApiLendAudit", ApiLendAuditDim);
        eventCodes.put("CombApiLendAudit", CombApiLendAuditDim);
    }

    public static Map<String, Double> getLastWeekCostTimeInfo(String eventCode){
        CsvReader reader = CsvUtil.getReader();
        //从文件中读取CSV数据
        CsvData data = reader.read(FileUtil.file("D:\\github\\tools_java\\src\\test\\resources\\costInfos\\" + eventCode + ".csv"), CharsetUtil.CHARSET_GBK);
        List<CsvRow> rows = data.getRows();
        Map<String, Double> costTimes = new HashMap<>();
        for(CsvRow row : rows){
            costTimes.put(row.get(0), Double.valueOf(row.get(1)));
        }
        return costTimes;
    }
}
