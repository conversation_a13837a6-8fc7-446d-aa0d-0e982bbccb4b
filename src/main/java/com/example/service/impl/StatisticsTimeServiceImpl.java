package com.example.service.impl;

import com.example.common.utils.AlertUtils;
import com.example.model.EventCodeCostTime;
import com.example.model.EventCodeNodeCostTime;
import com.example.service.StatisticsTimeService;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.influxdb.impl.InfluxDBResultMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static org.influxdb.querybuilder.BuiltQuery.QueryBuilder.*;
import static org.influxdb.querybuilder.time.DurationLiteral.*;

/**
 * <AUTHOR>
 */
@Service
public class StatisticsTimeServiceImpl implements StatisticsTimeService {


    @Resource
    AlertUtils alertUtils;

    private static final String RISK_ENGINE_DATABASE = "antifraud_risk_engine";
    private static final String RISK_GATEWAY_DATABASE = "antifraud_risk_gw";


    @Override
    public List<EventCodeCostTime> getTotalCostByEventCode(String eventCode, Long startTime, Long endTime) {

        Query command = select().mean("value").as("meanCost")
                .from(RISK_GATEWAY_DATABASE, "risk_gateway_gateway_notify")
                .where(eq("eventCode", eventCode))
                .where(gte("time", ti(startTime, MILLISECONDS)))
                .where(lte("time", ti(endTime, MILLISECONDS)))
                .where(lte("value", 240000))
                .groupBy(time(1L, MINUTE), "eventCode", "verifyResult", "isNewLoan")
                .fill("null");

        QueryResult queryResult = alertUtils.queryInfluxdb(RISK_GATEWAY_DATABASE, command);

        InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
        return resultMapper.toPOJO(queryResult, EventCodeCostTime.class);
    }

    @Override
    public List<EventCodeNodeCostTime> getNodeCostByEventCode(String eventCode, Long startTime, Long endTime){
        int costTime = 120000;
        if(eventCode.equals("haoHuanAmountRepay")){
            costTime = 50000;
        }
        Query command = select().mean("value").as("meanCost")
                .from(RISK_ENGINE_DATABASE, "risk_process_engine_callNodeNew_costTime")
                .where(eq("eventCode", eventCode))
                .where(gte("time", ti(startTime, MILLISECONDS)))
                .where(lte("time", ti(endTime, MILLISECONDS)))
                .where(lte("value", costTime))
                .groupBy(time(1L, MINUTE), "eventCode", "node", "verifyResult", "isNewLoan")
                .fill("null");
//                .tz("Asia/Shanghai")

        QueryResult queryResult = alertUtils.queryInfluxdb(RISK_ENGINE_DATABASE, command);

        InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
        return resultMapper.toPOJO(queryResult, EventCodeNodeCostTime.class);
    }
}
