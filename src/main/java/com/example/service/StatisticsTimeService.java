package com.example.service;

import com.example.model.EventCodeCostTime;
import com.example.model.EventCodeNodeCostTime;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface StatisticsTimeService {

    /**
     * 按事件统计耗时信息
     * @param eventCode
     * @return
     */
    List<EventCodeCostTime> getTotalCostByEventCode(String eventCode, Long startTime, Long endTime);

    List<EventCodeNodeCostTime> getNodeCostByEventCode(String eventCode, Long startTime, Long endTime);
}
