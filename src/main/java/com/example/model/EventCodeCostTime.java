package com.example.model;

import org.influxdb.annotation.Column;
import org.influxdb.annotation.Measurement;

import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Measurement(name = "risk_gateway_gateway_notify")
public class EventCodeCostTime {

    @Column(name = "time")
    private Instant time;

    @Column(name = "eventCode", tag = true)
    private String eventCode;

    @Column(name = "verifyResult", tag = true)
    private String verifyResult;

    @Column(name = "isNewLoan", tag = true)
    private String isNewLoan;

    @Column(name = "meanCost")
    private String meanCost;

    public String getTime() {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return formatter.format(Date.from(time));
    }

    public void setTime(Instant time) {
        this.time = time;
    }

    public String getEventCode() {
        return eventCode;
    }

    public void setEventCode(String eventCode) {
        this.eventCode = eventCode;
    }

    public String getVerifyResult() {
        return verifyResult;
    }

    public void setVerifyResult(String verifyResult) {
        this.verifyResult = verifyResult;
    }

    public String getIsNewLoan() {
        return isNewLoan;
    }

    public void setIsNewLoan(String isNewLoan) {
        this.isNewLoan = isNewLoan;
    }

    public Double getMeanCost() {
        return Double.parseDouble(meanCost);
    }

    public String getMeanCostStr(){
        return meanCost;
    }

    public void setMeanCost(String meanCost) {
        this.meanCost = meanCost;;
    }
}
