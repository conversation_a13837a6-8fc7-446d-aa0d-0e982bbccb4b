package com.example.tools;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.redisson.Redisson;
import org.redisson.RedissonDelayedQueue;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RDelayedQueue;
import org.redisson.api.RFuture;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static java.lang.Thread.sleep;

/**
 * <AUTHOR>
 */
public class DelayQueueTest {

    private static final Logger logger = LoggerFactory.getLogger(DelayQueueTest.class);

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    static class Message implements Serializable {
        private static final long serialVersionUID = 6151282779474571204L;

        private String id;

        private String data;
    }
    public static void main(String[] args) throws InterruptedException {
        int maxRoundCount = 10;
        int roundMessageCount = 1000;
        final CountDownLatch downLatch = new CountDownLatch(maxRoundCount);
        Config config = new Config();
        config.setNettyThreads(200);
        config.useSingleServer()
                .setTimeout(3000)
                .setKeepAlive(true)
                .setDatabase(1)
                .setPingConnectionInterval(3000)
                .setTcpNoDelay(true)
                .setRetryInterval(3000)
                .setRetryAttempts(4)
                .setIdleConnectionTimeout(5 * 60000)
                .setConnectionPoolSize(30)
                .setAddress("redis://127.0.0.1:6379");
//        Config config = new Config();
//        config.useSingleServer().setAddress("redis://127.0.0.1:6379");
        RedissonClient redissonClient = Redisson.create(config);
        Map<Integer, RBlockingQueue<Message>> queueMap = new HashMap<>();
        for (int roundId = 0 ; roundId < maxRoundCount; roundId++) {
            RBlockingQueue<Message> destinationQueue = redissonClient.getBlockingQueue("delay_shard_queue_"+roundId);
            RedissonDelayedQueue<Message> delayedQueue = (RedissonDelayedQueue<Message>)redissonClient.getDelayedQueue(destinationQueue);
            queueMap.put(roundId, destinationQueue);
        }
        for (int roundId = 0 ; roundId < maxRoundCount; roundId++) {
            final  int localRoundId = roundId;
            RBlockingQueue<Message> blockingQueue = queueMap.get(localRoundId);
            try{
                new Thread(() -> {
                    long maxDiff = 0L;
                    final AtomicInteger messageCount = new AtomicInteger(0);
                    //long sumDiff = 0L;
                    while (messageCount.get() < roundMessageCount) {
                        try {
                            Message ms = blockingQueue.take();
                            if (ms == null) {
                                continue;
                            }
                            Long actualDelayMs = (System.currentTimeMillis() - Long.parseLong(ms.getId()));
                            Long expectDelayMs = Integer.valueOf(ms.getData()) * 1000L;
                            Long diffDelayMs = Math.abs(actualDelayMs  - expectDelayMs);
                            maxDiff = maxDiff < diffDelayMs ? diffDelayMs : maxDiff;
                            //sumDiff += diffDelayMs;
                            logger.info("expectDelayMs: {}, actualDelayMs: {}, diffDelayMs: {}",expectDelayMs,actualDelayMs,diffDelayMs);
                            messageCount.incrementAndGet();
                        }catch (Throwable t) {
                            t.printStackTrace();
                        }
                    }
                    logger.info("## round: {}, message count: {}, maxDiff: {}",localRoundId,messageCount.get(),maxDiff);
                    downLatch.countDown();
                }).start();
            }catch (Throwable t) {
                t.printStackTrace();
            }
            new Thread(() -> {
                for (int i = 0 ; i < roundMessageCount; i++) {
                    long currentTime = System.currentTimeMillis();
                    Random r = new Random();
                    int n2 = r.nextInt(11);
                    Message message = new Message(currentTime + "", n2+"");
                    logger.info("message id=" + message.getId() + "data=" + message.getData());
                    RDelayedQueue<Message> delayedQueue = redissonClient.getDelayedQueue(blockingQueue);
                    RFuture<Void> future = delayedQueue.offerAsync(message, n2, TimeUnit.SECONDS);
                    future.onComplete((x,throwable) -> {
                        if (throwable != null) {
                            throwable.printStackTrace();
                        }
                    });
                    try {
                        sleep(10);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
                logger.info("send end...");
            }).start();
        }
        downLatch.await();
        redissonClient.shutdown(3000,6000,TimeUnit.MILLISECONDS);
    }
}
