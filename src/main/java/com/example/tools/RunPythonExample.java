package com.example.tools;

import org.apache.spark.deploy.PythonRunner;
import org.apache.spark.sql.SparkSession;

public class RunPythonExample {

    public static void main(String[] args) {
        String pyFilePath = "D:\\github\\tools_java\\src\\main\\resources\\py\\test.py";
        String pyFiles = "D:\\github\\tools_java\\src\\main\\resources\\py\\test.py";
        if(args.length == 2) {
            pyFilePath = args[0];
            pyFiles = args[1];
        }
        SparkSession spark = SparkSession
                .builder()
                .config("spark.pyspark.python", "python")
                .config("spark.pyspark.driver.python", "python")
                .appName("Java Spark SQL basic example")
                .master("local[*]")
                .getOrCreate();

        runPython(pyFilePath, pyFiles);

        spark.stop();
    }

    public static void runPython(String pyFilePath, String pyFiles){
        String inputPath = "-i /input";
        String outputPath = "-o /output";
        String[] pys = {pyFilePath, pyFiles, inputPath, outputPath};
        PythonRunner.main(pys);
    }
}
