package com.example.tools;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.util.HashMap;

public class JsoupHtmlTest {
    public static void main(String[] args) throws Exception{
        String html = "<!DOCTYPE html>\n" +
                "<html>\n" +
                "<head>\n" +
                "\t<meta charset=\"utf-8\">\n" +
                "\t<meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\n" +
                "\t<title>测试网页</title>\n" +
                "\t<link rel=\"stylesheet\" href=\"\">\n" +
                "</head>\n" +
                "<body>\n" +
                "\t<p>这是p标签的内容</p>\n" +
                "\t<a href=\"http://blog.beifengtz.com\" title=\"beifeng<PERSON>'s blog\">beifeng blog</a>\n" +
                "</body>\n" +
                "</html>";
        Document document = Jsoup.parse(html);  //  将字符串解析成Document对象
        System.out.println(document);

//        Document document = Jsoup.connect("http://blog.beifengtz.com/").get();
//        System.out.println(document);

        Document doc = Jsoup.connect("http://blog.beifengtz.com")  //  链接地址
                .data("query", "Java")    //  参数数据（可以传入Map对象）
                .userAgent("Mozilla")     //  使用代理
                .cookie("auth", "token")  //  传入cookie（可以传入Map对象）
                .timeout(3000)            //  请求超时时间
                .post();                  //  请求方法（post/get）


        Element div = doc.select("script").first();
        for (Element element : div.children()) {
            System.out.println(element.toString());
        }
    }

    public HashMap<String, String> convertCookie(String cookie) {
        HashMap<String, String> cookiesMap = new HashMap<String, String>();
        String[] items = cookie.trim().split(";");
        for (String item:items) {
            cookiesMap.put(item.split("=")[0], item.split("=")[1]);
        }
        return cookiesMap;
    }
}