package com.example.tools;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.MapDifference;
import com.google.common.collect.Maps;

import java.util.*;
import java.util.stream.IntStream;
import java.util.stream.Stream;

public class FlatMapUtil {
    private FlatMapUtil() {
        throw new AssertionError("No instances for you!");
    }

    public static Map<String, Object> flatten(Map<String, Object> map) {
        return map.entrySet().stream()
                .flatMap(FlatMapUtil::flatten)
                .collect(LinkedHashMap::new, (m, e) -> m.put("/" + e.getKey(), e.getValue()), LinkedHashMap::putAll);
    }

    private static Stream<Map.Entry<String, Object>> flatten(Map.Entry<String, Object> entry) {

        if (entry == null) {
            return Stream.empty();
        }

        if (entry.getValue() instanceof Map<?, ?>) {
            return ((Map<?, ?>) entry.getValue()).entrySet().stream()
                    .flatMap(e -> flatten(new AbstractMap.SimpleEntry<>(entry.getKey() + "/" + e.getKey(), e.getValue())));
        }

        if (entry.getValue() instanceof List<?>) {
            List<?> list = (List<?>) entry.getValue();
            return IntStream.range(0, list.size())
                    .mapToObj(i -> new AbstractMap.SimpleEntry<String, Object>(entry.getKey() + "/" + i, list.get(i)))
                    .flatMap(FlatMapUtil::flatten);
        }

        return Stream.of(entry);
    }

    public static MapDifference<String,Object> differenceTwoJson(String one, String two){
        ObjectMapper mapper = new ObjectMapper();

        TypeReference<HashMap<String, Object>> type =
                new TypeReference<HashMap<String, Object>>() {};


        HashMap<String, Object> j1 = null;
        HashMap<String, Object> j2 = null;
        try {
            j1 = mapper.readValue(one, type);
            j2 = mapper.readValue(two, type);
        } catch (JsonProcessingException e) {
            //
            System.out.println(e);
        }

        Map<String, Object> flatten1 = FlatMapUtil.flatten(j1);
        Map<String, Object> flatten2 = FlatMapUtil.flatten(j2);
        return Maps.difference(flatten1,flatten2);
    }

    /**
     * JSON转顺序排序的Map(为了方便后期获取数据,应答就不返回JSON字符串了,可自行去转换)
     *
     * @param jsonStr 原始json
     * @return 响应的map
     */
    public static Map<String, Object> jsonToMap(String jsonStr) {
        Map<String, Object> treeMap = new TreeMap();
        JSONObject json = JSONObject.parseObject(jsonStr, Feature.OrderedField);//Feature.OrderedField实现解析后保存不乱序
        Iterator<String> keys = json.keySet().iterator();
        while (keys.hasNext()) {
            String key = keys.next();
            Object value = json.get(key);
            //判断传入kay-value中是否含有""或null
            if (json.get(key) == null || value == null || value.toString().length() == 0) {
                //当JSON字符串存在null时,不将该kay-value放入Map中,即显示的结果不包括该kay-value
                continue;
            }
            // 判断是否为JSONArray(json数组)
            if (value instanceof JSONArray) {
                JSONArray jsonArray = (JSONArray) value;
                List<Object> arrayList = new ArrayList<>();
                for (Object object : jsonArray) {
                    // 判断是否为JSONObject，如果是 转化成TreeMap
                    if (object instanceof JSONObject) {
                        object = jsonToMap(object.toString());
                    }
                    arrayList.add(object);
                }
                treeMap.put(key, arrayList);
            } else {
                //判断该JSON中是否嵌套JSON
                boolean flag = isJSONValid(value.toString());
                if (flag) {
                    //若嵌套json了,通过递归再对嵌套的json(即子json)进行排序
                    value = jsonToMap(value.toString());
                }
                // 其他基础类型直接放入treeMap
                // JSONObject可进行再次解析转换
                treeMap.put(key, value);
            }
        }
        return treeMap;
    }

    /**
     * 校验是否是JSON字符串
     *
     * @param json 传入数据
     * @return 是JSON返回true, 否则false
     */
    public final static boolean isJSONValid(String json) {
        try {
            JSONObject.parseObject(json);
        } catch (JSONException ex) {
            return false;
        }
        return true;
    }


    public static void main(String[] args) throws JsonProcessingException {
//        String listN1 = "{\n" +
//                "    \"employee\":\n" +
//                "    {\n" +
//                "        \"id\": \"1212\",\n" +
//                "        \"fullName\": \"John Miles\",\n" +
//                "        \"age\": 34,\n" +
//                "        \"skills\": [\"C+++\", \"Java\", \"Python\"]\n" +
//                "    }\n" +
//                "}";
//
//        String listN2 = "{\n" +
//                "    \"employee\":\n" +
//                "    {\n" +
//                "        \"id\": \"1212\",\n" +
//                "        \"age\": 34,\n" +
//                "        \"fullName\": \"John Miles\",\n" +
//                "        \"skills\": [\"Java\", \"C++\", \"Python\"] \n" +
//                "    } \n" +
//                "}";
//        System.out.println(differenceTwoJson(listN1, listN2));

        String str = "{\"Head\":{\"version\":\"2.0\",\"chnlId\":\"shop\",\"txnTime\":\"20200722113700\",\"txnSeqNo\":\"shop20200722002009\",\"prodNo\":\"01\",\"Sign\":\"aaaaaaaaaaaa\"},\"Body\":{\"orderDesc\":{\"orderDate\":\"20200722\",\"orderId\":\"2020072200001\"},\"subOrder\":[{\"merInnerCd\":\"***************\",\"orgMerOrderId\":\"202007000000000001\",\"subAgreePrice\":[{\"a\":{\"cccc\":\"3333\",\"bbbb\":\"2222\",\"aaaa\":\"1111\"},\"b\":\"2222\",\"c\":\"3333\"},{\"b\":\"111\",\"a\":\"222\"}],\"subPointNum\":\"1\"},{\"merInnerCd\":\"20200**********\",\"merOrderId\":\"rderId56\",\"orgMerOrderId\":\"**************\",\"subPointNum\":\"2\"},[{\"merInnerCd\":\"***************\",\"orgMerOrderId\":\"202007000000000001\",\"subAgreePrice\":[{\"a\":{\"cccc\":\"3333\",\"bbbb\":\"2222\",\"aaaa\":\"1111\"},\"b\":\"2222\",\"c\":\"3333\"},{\"b\":\"111\",\"a\":\"222\"}],\"subPointNum\":\"1\"},{\"merInnerCd\":\"20200**********\",\"merOrderId\":\"rderId56\",\"orgMerOrderId\":\"**************\",\"subPointNum\":\"2\"}]],\"a\":\"10\",\"couponAmt\":\"\",\"couponBankAmt\":\"\",\"custId\":\"*********\",\"pointNum\":\"1\",\"txnAmt\":{\"cccc\":\"3333\",\"bbbb\":\"2222\",\"aaaa\":\"1111\"},\"txnSeqNo\":\"***************\"}}";
        //JAR包版本我用的是fastjson-1.2.47.jar
        JSONObject lwsxJson = JSON.parseObject(str);
        System.out.println("排序前的lwsxJson>>>" + JSON.toJSONString(lwsxJson));
        String rs = JSON.toJSONString(lwsxJson, SerializerFeature.MapSortField);
        //, SerializerFeature.PrettyFormat);
        System.out.println("排序后的lwsxJson>>>" + rs);

    }
}

