package com.example.tools;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.Connection;
import org.jsoup.Jsoup;

import java.io.IOException;
import java.net.Authenticator;
import java.net.InetSocketAddress;
import java.net.PasswordAuthentication;
import java.net.Proxy;

public class JsoupProxyDemo {

    /**
     * 信息获取
     */
    public static class JsoupUtils {
        /**
         * 通过代理访问信息
         * @param url 请求地址  带参数
         * @param proxyHost 代理ip
         * @param port 代理端口
         * @param loginUser 代理用户名
         * @param pwd 代理密码
         * @return
         */
        public static String getSimMessageWithProxy(String url,String proxyHost,int port,String loginUser,String pwd){
            try {
                //是否需要身份认证
                if(StringUtils.isNotBlank(loginUser)){
                    MyAuthenticator.setDefault(new MyAuthenticator(loginUser,pwd));
                }
                Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyHost, port));
                Connection.Response response =   Jsoup.connect(url).proxy(proxy).execute();
                System.out.println(response.body());
            } catch (IOException e) {
                e.printStackTrace();
            }
            return null;
        }

        /**
         * 访问信息
         * @param url  请求地址  带参数
         * @return
         */
        public static String getSimMessage(String url){
            try {
                Connection.Response response =   Jsoup.connect(url).execute();
                System.out.println(response.body());
            } catch (IOException e) {
                e.printStackTrace();
            }
            return null;
        }

        /**
         * 处理返回的信息
         * @return
         */
        private String handleMessage(){
            return null;
        }
    }

    /**
     * 重写代理认证
     */
    static class MyAuthenticator extends Authenticator {
        private String user = "";
        private String password = "";
        public MyAuthenticator(String user, String password) {
            this.user = user;
            this.password = password;
        }
        @Override
        protected PasswordAuthentication getPasswordAuthentication() {
            return new PasswordAuthentication(user, password.toCharArray());
        }

    }
}
