package com.example.tools;

import com.google.common.base.Preconditions;
import java.net.URL;
import java.net.URLClassLoader;
import org.apache.commons.lang.ArrayUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FileStatus;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.FsUrlStreamHandlerFactory;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.fs.PathFilter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * UdfClassLoader.
 */
public class UdfClassLoader extends URLClassLoader {

    private static final Logger LOGGER = LoggerFactory.getLogger(UdfClassLoader.class);

    static {
        try {
            URL.setURLStreamHandlerFactory(new FsUrlStreamHandlerFactory());
        } catch (Error e) {
            if (!e.getMessage().equals("factory already defined")) {
                throw new ExceptionInInitializerError(e);
            }
        }
    }

    private String directory;
    private String file;

    /**
     * Initialize a instance.
     *
     * @param directory hdfs directory path
     * @param file hdfs jar name prefix
     */
    public UdfClassLoader(String directory, String file) {
        super(new URL[] {}, UdfClassLoader.class.getClassLoader());

        try {
            Path hdfsDirectory = new Path(directory);

            FileSystem fs = hdfsDirectory.getFileSystem(new Configuration());

            FileStatus[] statuses =
                    fs.listStatus(
                            hdfsDirectory,
                            new PathFilter() {
                                @Override
                                public boolean accept(Path path) {
                                    return path.getName().startsWith(file);
                                }
                            });
            Preconditions.checkState(
                    ArrayUtils.isNotEmpty(statuses),
                    String.format("File %s not exist in directory %s", file, directory));

            Path jarPath = statuses[statuses.length - 1].getPath();
            LOGGER.info("Directory: {}, File: {}, Jar: {}", directory, file, jarPath.getName());

            addURL(jarPath.toUri().toURL());
        } catch (Exception e) {
            throw new ExceptionInInitializerError(e);
        }
    }
}
