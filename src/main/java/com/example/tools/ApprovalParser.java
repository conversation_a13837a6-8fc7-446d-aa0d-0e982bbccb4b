package com.example.tools;

import org.w3c.dom.*;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.ByteArrayInputStream;
import java.util.ArrayList;
import java.util.List;

public class ApprovalParser {

    public static List<String> getCurrentApprovers(String xmlContent) {
        List<String> approvers = new ArrayList<>();
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new ByteArrayInputStream(xmlContent.getBytes()));

            String createTime = document.getElementsByTagName("CreateTime").item(0).getTextContent();
            NodeList nodeList = document.getElementsByTagName("NodeList");

            for (int i = 0; i < nodeList.getLength(); i++) {
                Node node = nodeList.item(i);
                Element nodeElement = (Element) node;
                String nodeType = nodeElement.getElementsByTagName("NodeType").item(0).getTextContent();
                if ("1".equals(nodeType)) { // 节点类型 1 审批人 2 抄送人 3办理人
                    NodeList subNodeList = nodeElement.getElementsByTagName("SubNodeList");
                    for (int j = 0; j < subNodeList.getLength(); j++) {
                        Node subNode = subNodeList.item(j);
                        Element subNodeElement = (Element) subNode;
                        String sPtime = subNodeElement.getElementsByTagName("Sptime").item(0).getTextContent();
                        if (createTime.equals(sPtime)) {
                            String userId = subNodeElement.getElementsByTagName("UserId").item(0).getTextContent();
                            approvers.add(userId);
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return approvers;
    }

    public static void main(String[] args) {
//        String xmlContent = "<xml><ToUserName><![CDATA[ww63478d6a674cf652]]></ToUserName><FromUserName><![CDATA[sys]]></FromUserName><CreateTime>1727674468</CreateTime><MsgType><![CDATA[event]]></MsgType><Event><![CDATA[sys_approval_change]]></Event><AgentID>3010040</AgentID><ApprovalInfo><SpNo>202409300012</SpNo><SpName><![CDATA[策略上线申请]]></SpName><SpStatus>2</SpStatus><TemplateId><![CDATA[3WLJ77GpukjfYji8sVR5pkQk93awMwuqHB43pfTa]]></TemplateId><ApplyTime>1727674341</ApplyTime><Applyer><UserId><![CDATA[tanghaorong]]></UserId><Party><![CDATA[1000004169]]></Party></Applyer><SpRecord><SpStatus>2</SpStatus><ApproverAttr>1</ApproverAttr><Details><Approver><UserId><![CDATA[yanrui]]></UserId></Approver><Speech><![CDATA[]]></Speech><SpStatus>2</SpStatus><SpTime>1727674421</SpTime></Details></SpRecord><SpRecord><SpStatus>2</SpStatus><ApproverAttr>1</ApproverAttr><Details><Approver><UserId><![CDATA[chang.liu]]></UserId></Approver><Speech><![CDATA[]]></Speech><SpStatus>2</SpStatus><SpTime>1727674468</SpTime></Details></SpRecord><Notifyer><UserId><![CDATA[zhanglinxiao]]></UserId></Notifyer><Notifyer><UserId><![CDATA[qianben]]></UserId></Notifyer><Notifyer><UserId><![CDATA[hejunhao]]></UserId></Notifyer><Notifyer><UserId><![CDATA[zhangrujian]]></UserId></Notifyer><Notifyer><UserId><![CDATA[lilusong]]></UserId></Notifyer><Notifyer><UserId><![CDATA[tanghaorong]]></UserId></Notifyer><Notifyer><UserId><![CDATA[wangle03]]></UserId></Notifyer><StatuChangeEvent>2</StatuChangeEvent><ProcessList><NodeList><NodeType>2</NodeType><SubNodeList><UserInfo><UserId><![CDATA[zhanglinxiao]]></UserId></UserInfo></SubNodeList><SubNodeList><UserInfo><UserId><![CDATA[qianben]]></UserId></UserInfo></SubNodeList><SubNodeList><UserInfo><UserId><![CDATA[hejunhao]]></UserId></UserInfo></SubNodeList><SubNodeList><UserInfo><UserId><![CDATA[zhangrujian]]></UserId></UserInfo></SubNodeList><SubNodeList><UserInfo><UserId><![CDATA[lilusong]]></UserId></UserInfo></SubNodeList><SubNodeList><UserInfo><UserId><![CDATA[tanghaorong]]></UserId></UserInfo></SubNodeList><SubNodeList><UserInfo><UserId><![CDATA[wangle03]]></UserId></UserInfo></SubNodeList></NodeList><NodeList><NodeType>1</NodeType><SpStatus>2</SpStatus><ApvRel>2</ApvRel><SubNodeList><UserInfo><UserId><![CDATA[yanrui]]></UserId></UserInfo><Speech><![CDATA[]]></Speech><SpYj>2</SpYj><Sptime>1727674421</Sptime></SubNodeList></NodeList><NodeList><NodeType>1</NodeType><SpStatus>2</SpStatus><ApvRel>2</ApvRel><SubNodeList><UserInfo><UserId><![CDATA[chang.liu]]></UserId></UserInfo><Speech><![CDATA[]]></Speech><SpYj>2</SpYj><Sptime>1727674468</Sptime></SubNodeList></NodeList></ProcessList></ApprovalInfo></xml>";
        String xmlContent = "<xml><ToUserName><![CDATA[ww63478d6a674cf652]]></ToUserName><FromUserName><![CDATA[sys]]></FromUserName><CreateTime>1727674421</CreateTime><MsgType><![CDATA[event]]></MsgType><Event><![CDATA[sys_approval_change]]></Event><AgentID>3010040</AgentID><ApprovalInfo><SpNo>202409300012</SpNo><SpName><![CDATA[策略上线申请]]></SpName><SpStatus>1</SpStatus><TemplateId><![CDATA[3WLJ77GpukjfYji8sVR5pkQk93awMwuqHB43pfTa]]></TemplateId><ApplyTime>1727674341</ApplyTime><Applyer><UserId><![CDATA[tanghaorong]]></UserId><Party><![CDATA[1000004169]]></Party></Applyer><SpRecord><SpStatus>2</SpStatus><ApproverAttr>1</ApproverAttr><Details><Approver><UserId><![CDATA[yanrui]]></UserId></Approver><Speech><![CDATA[]]></Speech><SpStatus>2</SpStatus><SpTime>1727674421</SpTime></Details></SpRecord><SpRecord><SpStatus>1</SpStatus><ApproverAttr>1</ApproverAttr><Details><Approver><UserId><![CDATA[chang.liu]]></UserId></Approver><Speech><![CDATA[]]></Speech><SpStatus>1</SpStatus><SpTime>0</SpTime></Details></SpRecord><Notifyer><UserId><![CDATA[zhanglinxiao]]></UserId></Notifyer><Notifyer><UserId><![CDATA[qianben]]></UserId></Notifyer><Notifyer><UserId><![CDATA[hejunhao]]></UserId></Notifyer><Notifyer><UserId><![CDATA[zhangrujian]]></UserId></Notifyer><Notifyer><UserId><![CDATA[lilusong]]></UserId></Notifyer><Notifyer><UserId><![CDATA[tanghaorong]]></UserId></Notifyer><Notifyer><UserId><![CDATA[wangle03]]></UserId></Notifyer><StatuChangeEvent>2</StatuChangeEvent><ProcessList><NodeList><NodeType>2</NodeType><SubNodeList><UserInfo><UserId><![CDATA[zhanglinxiao]]></UserId></UserInfo></SubNodeList><SubNodeList><UserInfo><UserId><![CDATA[qianben]]></UserId></UserInfo></SubNodeList><SubNodeList><UserInfo><UserId><![CDATA[hejunhao]]></UserId></UserInfo></SubNodeList><SubNodeList><UserInfo><UserId><![CDATA[zhangrujian]]></UserId></UserInfo></SubNodeList><SubNodeList><UserInfo><UserId><![CDATA[lilusong]]></UserId></UserInfo></SubNodeList><SubNodeList><UserInfo><UserId><![CDATA[tanghaorong]]></UserId></UserInfo></SubNodeList><SubNodeList><UserInfo><UserId><![CDATA[wangle03]]></UserId></UserInfo></SubNodeList></NodeList><NodeList><NodeType>1</NodeType><SpStatus>2</SpStatus><ApvRel>2</ApvRel><SubNodeList><UserInfo><UserId><![CDATA[yanrui]]></UserId></UserInfo><Speech><![CDATA[]]></Speech><SpYj>2</SpYj><Sptime>1727674421</Sptime></SubNodeList></NodeList><NodeList><NodeType>1</NodeType><SpStatus>1</SpStatus><ApvRel>2</ApvRel><SubNodeList><UserInfo><UserId><![CDATA[chang.liu]]></UserId></UserInfo><Speech><![CDATA[]]></Speech><SpYj>1</SpYj><Sptime>0</Sptime></SubNodeList></NodeList></ProcessList></ApprovalInfo></xml>";
        List<String> approvers = getCurrentApprovers(xmlContent);
        System.out.println("Current Approvers: " + approvers);
    }
}