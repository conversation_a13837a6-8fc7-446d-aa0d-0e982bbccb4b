package com.example.tools;

import org.bouncycastle.openssl.PEMParser;
import org.bouncycastle.openssl.jcajce.JcaPEMKeyConverter;

import java.io.IOException;
import java.io.Reader;
import java.io.StringReader;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;

public class ECSignature {
    public static void main(String[] args) throws Exception {
        // 读取PEM格式的私钥文件
//        PrivateKey privateKey = getPrivateKeyFromPEM("src/main/resources/pri_key.pem");
//        System.out.println(privateKey);

            SO22963581BCPEMPrivateEC();
    }

    static void SO22963581BCPEMPrivateEC () throws Exception {
        Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
        Reader rdr = new StringReader("-----BEGIN EC PRIVATE KEY-----\n" +
                "MIGkAgEBBDDnNSOdT/6AZAg3c7oqPUbmTV0NU9AOjD4AVpDHgCXP3J+EH/1lqx0C\n" +
                "L6IrOVrN9/GgBwYFK4EEACKhZANiAAR0dl2lvWvsPERGYY6FCr7gI3TaQNmiRsrD\n" +
                "+cl/uHHWuQkzJASFYe27yT0mOYtifMWHztGc7g/HsC3gXZBIT2ns6KhBAHRNtm2Y\n" +
                "c0W7K7eTHBMMR9AX7gYJ7PzVVgOtDs8=\n" +
                "-----END EC PRIVATE KEY-----");
        Object parsed = new org.bouncycastle.openssl.PEMParser(rdr).readObject();
        KeyPair pair = new org.bouncycastle.openssl.jcajce.JcaPEMKeyConverter().getKeyPair((org.bouncycastle.openssl.PEMKeyPair)parsed);
        PrivateKey privateKey = pair.getPrivate();
        System.out.println(privateKey.getEncoded());
    }

    public static PrivateKey getPrivateKeyFromPEM(String filePath)
            throws IOException, NoSuchAlgorithmException, InvalidKeySpecException {
        // 读取PEM格式的私钥文件
        byte[] keyBytes = Files.readAllBytes(Paths.get(filePath));
        String keyString = new String(keyBytes);

        // Remove EC PARAMETERS section
        keyString = keyString.replaceAll("-----BEGIN EC PARAMETERS-----.*?-----END EC PARAMETERS-----", "");

        // Remove PEM file headers, footers, and newlines
        String privKeyPEM = keyString
                .replace("-----BEGIN EC PRIVATE KEY-----", "")
                .replace("-----END EC PRIVATE KEY-----", "")
                .replaceAll("\\s+", "");

        // Ensure no illegal characters are present
        privKeyPEM = privKeyPEM.replaceAll("[^A-Za-z0-9+/=]", "");

        // 解码Base64编码的私钥
        byte[] encoded = Base64.getDecoder().decode(privKeyPEM);
        KeyFactory keyFactory = KeyFactory.getInstance("EC");
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(encoded);
        return keyFactory.generatePrivate(keySpec);
    }
}