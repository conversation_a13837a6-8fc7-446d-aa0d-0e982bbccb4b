package com.example.tools;

import com.alibaba.fastjson.JSON;

import java.io.*;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class ConcurrentHashMapSerializationTest {

    public static void main(String[] args) {
        Map<String, Object> originalMap = new ConcurrentHashMap<>();
        originalMap.put("key1", "value1");
        originalMap.put("key2", 123);
        originalMap.put("key3", true);

        try {
            ByteArrayOutputStream byteOutStream = new ByteArrayOutputStream();
            ObjectOutputStream objectOutStream = new ObjectOutputStream(byteOutStream);
            objectOutStream.writeObject(originalMap);
            objectOutStream.flush();
            byte[] serializedBytes = byteOutStream.toByteArray();

            ByteArrayInputStream byteInStream = new ByteArrayInputStream(serializedBytes);
            ObjectInputStream objectInStream = new ObjectInputStream(byteInStream);
            Map<String, Object> deserializedMap = (Map<String, Object>) objectInStream.readObject();

            System.out.println("Original Map: " + JSON.toJSONString(originalMap));
            System.out.println("Deserialized Map: " + JSON.toJSONString(deserializedMap));
            System.out.println("Are both maps equal? " + originalMap.equals(deserializedMap));

        } catch (IOException | ClassNotFoundException e) {
            e.printStackTrace();
        }
    }
}