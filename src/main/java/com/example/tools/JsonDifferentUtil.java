package com.example.tools;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.example.dto.DifferentDataDTO;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * 获取json不一致数据
 */
public class JsonDifferentUtil {

    /**
     * jsonObejct 比较
     *
     * @param newJson
     * @param oldJson
     * @param key
     * @return
     */
    public static List<DifferentDataDTO> compareJson(JSONObject newJson, JSONObject oldJson, String key) {
        List<DifferentDataDTO> list = new ArrayList<>();

        // 新旧数据都不为空
        if(newJson != null && oldJson != null ){
            Iterator<String> i = newJson.keySet().iterator();
            while (i.hasNext()) {
                key = i.next();
                // 获取Object 进行比较
                list.addAll(compareJson(newJson.get(key), oldJson.get(key), key));
            }
        }else{// 新旧数据 有一个为空
            DifferentDataDTO differentDataDTO = new DifferentDataDTO();
            differentDataDTO.setFieldName(key);

            if (newJson == null || oldJson == null) {
                differentDataDTO.setNewFieldValue(JSONObject.toJSONString(newJson));
                differentDataDTO.setOldFieldValue(JSONObject.toJSONString(oldJson));
                list.add(differentDataDTO);
            }
            // 其他情况不做处理
        }
        return list;
    }

    /**
     * Object 比较
     *
     * @param newObj
     * @param oldObj
     * @param key
     * @return
     */
    public static List<DifferentDataDTO> compareJson(Object newObj, Object oldObj, String key) {
        List<DifferentDataDTO> list = new ArrayList<>();
        // 判断类型 JsonObject 则按照 JsonObject 进行比较
        if (newObj instanceof JSONObject) {
            list.addAll(compareJson((JSONObject) newObj, (JSONObject) oldObj, key));
            // 判断类型 JsonArray 则按照 JsonArray 进行比较
        } else if (newObj instanceof JSONArray) {
            list.addAll(compareJson((JSONArray) newObj, (JSONArray) oldObj, key));
            // 判断类型 String 则按照 String 进行比较
        } else if (newObj instanceof String) {
            String json1ToStr = newObj.toString();
            String json2ToStr = oldObj.toString();
            list.addAll(compareJson(json1ToStr, json2ToStr, key));
        } else {
            // 其他类型 按照 String 处理
            list.addAll(compareJson(newObj+"", oldObj+"", key));
        }
        return list;
    }

    /**
     * 字符串比较
     *
     * @param newStr
     * @param oldStr
     * @param key
     * @return
     */
    public static List<DifferentDataDTO> compareJson(String newStr, String oldStr, String key) {
        List<DifferentDataDTO> list = new ArrayList<>();
        DifferentDataDTO differentDataDTO = new DifferentDataDTO();
        if (!newStr.equals(oldStr)) {
            differentDataDTO.setFieldName(key);
            differentDataDTO.setNewFieldValue(newStr);
            differentDataDTO.setOldFieldValue(oldStr);
            list.add(differentDataDTO);
        }
        // 一致,不做处理
        return list;
    }

    /**
     * JsonArray 比较
     *
     * @param newJsonArray
     * @param oldjsonArray
     * @param key
     * @return
     */
    @SuppressWarnings("rawtypes")
    public static List<DifferentDataDTO> compareJson(JSONArray newJsonArray, JSONArray oldjsonArray, String key) {
        List<DifferentDataDTO> list = new ArrayList<>();
        // 新旧数据都不为空时，处理json数组内容
        if (newJsonArray != null && oldjsonArray != null) {
            Iterator i1 = newJsonArray.iterator();
            Iterator i2 = oldjsonArray.iterator();
            while (i1.hasNext()) {
                // 获取Object 进行比较
                list.addAll(compareJson(i1.next(), i2.next(), key));
            }
        } else {// 新旧数据 有一个为空
            DifferentDataDTO differentDataDTO = new DifferentDataDTO();
            differentDataDTO.setFieldName(key);

            if (newJsonArray == null || oldjsonArray == null) {
                differentDataDTO.setNewFieldValue(JSONObject.toJSONString(newJsonArray));
                differentDataDTO.setOldFieldValue(JSONObject.toJSONString(oldjsonArray));
                list.add(differentDataDTO);
            }
            // 其他情况不做处理
        }
        return list;
    }
}