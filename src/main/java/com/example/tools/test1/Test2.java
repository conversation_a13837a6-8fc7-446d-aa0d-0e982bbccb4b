package com.example.tools.test1;

public class Test2 {

    private static VMShow StaticVmShow = new VMShow();
    private VMShow objVmShow = new VMShow();

    public static void main(String[] args) {
        fn();
    }

    private static VMShow fn(){
        return new VMShow();
    }
}

class VMShow {
    private int basicInt = 1;
    private Integer objInt = 2;
    private static Integer staticInt = 3;
    private String basicString = "basicString";
    private static String staticString = new String("staticString");
}
