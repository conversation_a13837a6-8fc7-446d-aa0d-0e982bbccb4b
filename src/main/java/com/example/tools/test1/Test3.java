package com.example.tools.test1;



import org.apache.commons.lang3.StringUtils;


public class Test3 {

    private static final String[] partitions = new String[]{"0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "a", "b", "c", "d", "e", "f", "g"};

    public static void main(String[] args) {
//        AtomicBoolean isFailed = new AtomicBoolean(false);
//        isFailed.compareAndSet(false, true);
//        System.out.println(isFailed.get());
        String userId = "22544605";
        long currentTime = System.currentTimeMillis() * 1000000L + System.nanoTime() % 1000000L; //当前纳秒时间戳
        String rowKey = partitions[(int) (Long.parseLong(userId) % 17)] + StringUtils.leftPad(userId, 19, "0") + (Long.MAX_VALUE - currentTime);
        String rowKeyPrex = partitions[(int) (Long.parseLong(userId) % 17)] + StringUtils.leftPad(userId, 19, "0");
        System.out.println(rowKeyPrex);
    }
}
