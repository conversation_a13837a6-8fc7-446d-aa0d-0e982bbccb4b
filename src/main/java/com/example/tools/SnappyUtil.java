package com.example.tools;

import java.io.IOException;
import org.xerial.snappy.Snappy;


public class SnappyUtil {
    public static String compress(String info) throws IOException {
        return byteTohex(Snappy.compress(info, "ISO-8859-1"));
    }
    public static String uncompress(String info) throws IOException {
        return Snappy.uncompressString(hexTobyte(info), "ISO-8859-1");
    }
    private static String byteTohex(byte[] b) {
        String hs = "";
        String stmp = "";
        for (int n = 0; n < b.length; n++) {
            stmp = (java.lang.Integer.toHexString(b[n] & 0XFF));
            if (stmp.length() == 1) {
                hs = hs + "0" + stmp;
            } else {
                hs = hs + stmp;
            }
        }
        return hs;
    }
    private static byte[] hexTobyte(String str) {
        if (str == null) {
            return null;
        }
        str = str.trim();
        int len = str.length();
        if (len == 0 || len % 2 == 1) {
            return null;
        }
        byte[] b = new byte[len / 2];
        try {
            for (int i = 0; i < str.length(); i += 2) {
                b[i / 2] = (byte) Integer.decode("0x" + str.substring(i, i + 2)).intValue();
            }
            return b;
        } catch (Exception e) {
            return null;
        }
    }
}
