package com.example.tools;

import com.gargoylesoftware.htmlunit.WebClient;
import com.gargoylesoftware.htmlunit.html.HtmlPage;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;

import java.io.IOException;

public class Htmlunit {
    public static void main(String[] args) throws IOException {
        //Htmlunit模拟的浏览器，设置css,js等支持及其它的一些简单设置
        WebClient browser = new WebClient();
        browser.getOptions().setCssEnabled(false);
        browser.getOptions().setJavaScriptEnabled(true);
        browser.getOptions().setThrowExceptionOnScriptError(false);

        //获取页面
        HtmlPage htmlPage = browser.getPage("http://www.baidu.com");
        //设置等待js的加载时间
        browser.waitForBackgroundJavaScript(3000);

        //使用xml的方式解析获取到jsoup的document对象
        Document doc = Jsoup.parse(htmlPage.asXml());
        System.out.println(doc);
    }
}
