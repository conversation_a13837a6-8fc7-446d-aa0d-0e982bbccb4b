package com.example.tools;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeType;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.avro.Schema;
import org.apache.avro.generic.GenericDatumReader;
import org.apache.avro.io.DecoderFactory;
import org.apache.avro.io.JsonDecoder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.*;

/**
 */
public class AvroConverter {

    private static final Logger logger = LoggerFactory.getLogger(AvroConverter.class);

    private static final String NAME = "name";
    private static final String TYPE = "type";
    private static final String ARRAY = "array";
    private static final String ITEMS = "items";
    private static final String STRING = "string";
    private static final String RECORD = "record";
    private static final String FIELDS = "fields";
    private static final String NULL = "null";
    private static final String BOOLEAN = "boolean";
    private static final String LONG = "long";
    private static final String DOUBLE = "double";

    private final ObjectMapper mapper;

    /**
     * Constructor
     *
     * @param mapper to serialize
     */
    public AvroConverter(final ObjectMapper mapper) {
        this.mapper = mapper;
    }

    /**
     * validation
     *
     * @param avroSchemaString to validate
     * @param jsonString       to validate
     * @return true if validated, false otherwise
     * @throws IOException
     */
    @SuppressWarnings("unchecked")
    public boolean validate(final String avroSchemaString, final String jsonString) throws IOException {
        Schema.Parser t = new Schema.Parser();
        Schema schema = t.parse(avroSchemaString);
        GenericDatumReader reader = new GenericDatumReader(schema);
        JsonDecoder decoder = DecoderFactory.get().jsonDecoder(schema, jsonString);
        reader.read(null, decoder);
        return true;
    }

    /**
     * convert to avro schema
     *
     * @param json to convert
     * @return avro schema json
     * @throws IOException
     */
    public String convert(final String json) throws IOException {
        final JsonNode jsonNode = mapper.readTree(json);
        final ObjectNode finalSchema = mapper.createObjectNode();
        finalSchema.put("namespace", "com.risk.avro");
        finalSchema.put(NAME, "outer_record");
        finalSchema.put(TYPE, RECORD);
        finalSchema.set(FIELDS, getFields(jsonNode));
        return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(finalSchema);
    }

    /**
     * @param jsonNode to getFields
     * @return array nodes of fields
     */
    private ArrayNode getFields(final JsonNode jsonNode) {
        final ArrayNode fields = mapper.createArrayNode();
        final Iterator<Map.Entry<String, JsonNode>> elements = jsonNode.fields();

        Map.Entry<String, JsonNode> map;
        while (elements.hasNext()) {
            map = elements.next();
            final JsonNode nextNode = map.getValue();

            switch (nextNode.getNodeType()) {
                case NUMBER:
                    fields.add(mapper.createObjectNode().put(NAME, map.getKey()).put(TYPE, (nextNode.isLong() ? "long" : "double")).put("default", -999.0));
                    break;
                case STRING:
                    fields.add(mapper.createObjectNode().put(NAME, map.getKey()).put(TYPE, STRING).put("default", "None"));
                    break;
                case ARRAY:
                    final ArrayNode arrayNode = (ArrayNode) nextNode;
                    final ObjectNode objectNode = mapper.createObjectNode();
                    objectNode.put(NAME, map.getKey());
                    if (arrayNode.isEmpty()) {
                        throw new RuntimeException("Unable to guess at schema type for empty array");
                    }

                    Set<Object> itemsObject = new HashSet<>();
                    boolean isGeneralType = true;
                    for (JsonNode itemNode : arrayNode) {
                        if (itemNode.getNodeType() == JsonNodeType.NUMBER) {
                            itemsObject.add(nextNode.isLong() ? LONG : DOUBLE);
                        } else if (itemNode.getNodeType() == JsonNodeType.STRING) {
                            itemsObject.add(STRING);
                        } else {
                            isGeneralType = false;
                            itemsObject.add(getFields(itemNode));
                        }
                    }
                    if (isGeneralType) {
                        if(itemsObject.size() == 1){
                            objectNode.set(TYPE, mapper.createObjectNode().put(TYPE, ARRAY).put(ITEMS, (String)parseItems(itemsObject, isGeneralType)));
                        }else {
                            objectNode.set(TYPE, mapper.createObjectNode().put(TYPE, ARRAY).set(ITEMS, (ArrayNode)parseItems(itemsObject, isGeneralType)));
                        }

                    } else {
                        objectNode.set(TYPE, mapper.createObjectNode().put(TYPE, ARRAY).set(ITEMS,
                                mapper.createObjectNode()
                                        .put(TYPE, RECORD)
                                        .put(NAME, generateRandomNumber(map))
                                        .set(FIELDS, (ArrayNode)parseItems(itemsObject, isGeneralType))
                        ));
                    }
                    fields.add(objectNode);
                    break;
                case OBJECT:
                    ObjectNode node = mapper.createObjectNode();
                    node.put(NAME, map.getKey());
                    node.set(TYPE, mapper.createObjectNode().put(TYPE, RECORD).put(NAME, generateRandomNumber(map)).set(FIELDS, getFields(nextNode)));
                    fields.add(node);
                    break;
                case NULL:
                    ObjectNode unionNullNode = mapper.createObjectNode();
                    unionNullNode.put(NAME, map.getKey());
                    unionNullNode.putArray(TYPE).add(NULL);
                    fields.add(unionNullNode);
                    break;
                case BOOLEAN:
                    fields.add(mapper.createObjectNode().put(NAME, map.getKey()).put(TYPE, BOOLEAN).put("default", false));
                    break;
                default:
                    logger.error("Node type not found - " + nextNode.getNodeType());
                    throw new RuntimeException("Unable to determine action for ndoetype "+nextNode.getNodeType()+"; Allowed types are ARRAY, STRING, NUMBER, OBJECT");
            }
        }
        return fields;
    }

    /**
     *
     * @param itemsTypeSet
     * @param isGeneralType
     * @return
     */
    public Object parseItems(Set<Object> itemsTypeSet, boolean isGeneralType){
        if(itemsTypeSet.size() == 1){
            return itemsTypeSet.iterator().next();
        }
        ObjectMapper objectMapper = new ObjectMapper();
        ArrayNode arrayNode = objectMapper.createArrayNode();
        if(isGeneralType){
            itemsTypeSet.forEach(i -> arrayNode.add((String)i));
        }else {
            Map<String, Set<String>> itemsMap = new HashMap<>();
            for (Object itemType : itemsTypeSet) {
                JsonNode itemJsonType = (JsonNode) itemType;
                Iterator<JsonNode> elements = itemJsonType.elements();
                while (elements.hasNext()){
                    JsonNode next = elements.next();
                    String name = next.get(NAME).asText();
                    String type = next.get(TYPE).asText();

                    Set<String> orDefault = itemsMap.getOrDefault(name, new HashSet<>());
                    orDefault.add(type);
                    itemsMap.put(name, orDefault);
                }
            }

            for (String name : itemsMap.keySet()) {
                ArrayNode arrayNode1 = objectMapper.createArrayNode();
                ObjectNode objectNode = objectMapper.createObjectNode();
                if(itemsMap.get(name).size() == 1){
                    objectNode.put(NAME, name);
                    objectNode.put(TYPE, itemsMap.get(name).iterator().next());
                }else {
                    itemsMap.get(name).forEach(arrayNode1::add);
                    objectNode.put(NAME, name);
                    objectNode.putIfAbsent(TYPE, arrayNode1);
                }
                arrayNode.add(objectNode);
            }
        }
        return arrayNode;
    }

    /**
     * @param map to create random number
     * @return random
     */
    private String generateRandomNumber(Map.Entry<String, JsonNode> map) {
        return (map.getKey() + "_" + new Random().nextInt(100));
    }
}
