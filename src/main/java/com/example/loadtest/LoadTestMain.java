package com.example.loadtest;

import org.apache.commons.cli.*;

/**
 * HTTP负载测试工具主类
 * 处理命令行参数并启动负载测试
 */
public class LoadTestMain {
    
    private static final String VERSION = "1.0.0";
    
    public static void main(String[] args) {
        try {
            // 解析命令行参数
            LoadTestConfig config = parseCommandLineArgs(args);
            
            // 验证配置
            config.validate();
            
            // 创建组件
            StatisticsCollector statisticsCollector = new StatisticsCollector();
            AsyncHttpClient httpClient = new AsyncHttpClient(config, statisticsCollector);
            MetricsReporter metricsReporter = new MetricsReporter(statisticsCollector, httpClient, config);
            LoadTestExecutor executor = new LoadTestExecutor(config, httpClient, statisticsCollector, metricsReporter);
            
            // 注册关闭钩子
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                System.out.println("\n收到关闭信号，正在优雅关闭...");
                executor.shutdown();
            }));
            
            // 启动测试
            executor.startTest();
            
            // 等待测试完成
            executor.waitForCompletion();
            
            // 清理资源
            executor.shutdown();
            
        } catch (Exception e) {
            System.err.println("错误: " + e.getMessage());
            System.exit(1);
        }
    }
    
    /**
     * 解析命令行参数
     */
    private static LoadTestConfig parseCommandLineArgs(String[] args) throws ParseException {
        Options options = createOptions();
        CommandLineParser parser = new DefaultParser();
        CommandLine cmd = parser.parse(options, args);
        
        // 显示帮助信息
        if (cmd.hasOption("help")) {
            printHelp(options);
            System.exit(0);
        }
        
        // 显示版本信息
        if (cmd.hasOption("version")) {
            System.out.println("HTTP负载测试工具 v" + VERSION);
            System.exit(0);
        }
        
        // 检查必需参数
        if (!cmd.hasOption("url")) {
            System.err.println("错误: 必须指定目标URL");
            printHelp(options);
            System.exit(1);
        }
        
        LoadTestConfig config = new LoadTestConfig();
        
        // 基本配置
        config.setTargetUrl(cmd.getOptionValue("url"));
        
        if (cmd.hasOption("method")) {
            config.setHttpMethod(cmd.getOptionValue("method"));
        }
        
        if (cmd.hasOption("rps")) {
            config.setRequestsPerSecond(Integer.parseInt(cmd.getOptionValue("rps")));
        }
        
        if (cmd.hasOption("duration")) {
            config.setDurationSeconds(Integer.parseInt(cmd.getOptionValue("duration")));
        }
        
        if (cmd.hasOption("requests")) {
            config.setTotalRequests(Integer.parseInt(cmd.getOptionValue("requests")));
        }
        
        if (cmd.hasOption("concurrency")) {
            config.setConcurrency(Integer.parseInt(cmd.getOptionValue("concurrency")));
        }
        
        // HTTP配置
        if (cmd.hasOption("header")) {
            String[] headers = cmd.getOptionValues("header");
            for (String header : headers) {
                String[] parts = header.split(":", 2);
                if (parts.length == 2) {
                    config.addHeader(parts[0].trim(), parts[1].trim());
                }
            }
        }
        
        if (cmd.hasOption("body")) {
            config.setRequestBody(cmd.getOptionValue("body"));
        }
        
        if (cmd.hasOption("content-type")) {
            config.setContentType(cmd.getOptionValue("content-type"));
        }
        
        // 连接配置
        if (cmd.hasOption("connect-timeout")) {
            config.setConnectionTimeout(Integer.parseInt(cmd.getOptionValue("connect-timeout")));
        }
        
        if (cmd.hasOption("read-timeout")) {
            config.setReadTimeout(Integer.parseInt(cmd.getOptionValue("read-timeout")));
        }
        
        if (cmd.hasOption("max-connections")) {
            config.setMaxTotalConnections(Integer.parseInt(cmd.getOptionValue("max-connections")));
        }
        
        if (cmd.hasOption("max-connections-per-host")) {
            config.setMaxConnectionsPerHost(Integer.parseInt(cmd.getOptionValue("max-connections-per-host")));
        }
        
        if (cmd.hasOption("no-keepalive")) {
            config.setKeepAlive(false);
        }
        
        // 报告配置
        if (cmd.hasOption("report-interval")) {
            config.setReportIntervalSeconds(Integer.parseInt(cmd.getOptionValue("report-interval")));
        }
        
        if (cmd.hasOption("output")) {
            config.setOutputFile(cmd.getOptionValue("output"));
        }
        
        if (cmd.hasOption("no-detailed-metrics")) {
            config.setEnableDetailedMetrics(false);
        }
        
        return config;
    }
    
    /**
     * 创建命令行选项
     */
    private static Options createOptions() {
        Options options = new Options();
        
        // 基本选项
        options.addOption(Option.builder("u")
            .longOpt("url")
            .hasArg()
            .required()
            .desc("目标URL (必需)")
            .build());
        
        options.addOption(Option.builder("m")
            .longOpt("method")
            .hasArg()
            .desc("HTTP方法 (默认: GET)")
            .build());
        
        options.addOption(Option.builder("r")
            .longOpt("rps")
            .hasArg()
            .type(Number.class)
            .desc("每秒请求数 (默认: 1000)")
            .build());
        
        options.addOption(Option.builder("d")
            .longOpt("duration")
            .hasArg()
            .type(Number.class)
            .desc("测试持续时间(秒) (默认: 60)")
            .build());
        
        options.addOption(Option.builder("n")
            .longOpt("requests")
            .hasArg()
            .type(Number.class)
            .desc("总请求数 (如果指定，将忽略duration)")
            .build());
        
        options.addOption(Option.builder("c")
            .longOpt("concurrency")
            .hasArg()
            .type(Number.class)
            .desc("并发数 (默认: 100)")
            .build());
        
        // HTTP选项
        options.addOption(Option.builder("H")
            .longOpt("header")
            .hasArg()
            .desc("添加HTTP头部 (格式: 'Key: Value')")
            .build());
        
        options.addOption(Option.builder("b")
            .longOpt("body")
            .hasArg()
            .desc("请求体内容")
            .build());
        
        options.addOption(Option.builder("t")
            .longOpt("content-type")
            .hasArg()
            .desc("Content-Type头部 (默认: application/json)")
            .build());
        
        // 连接选项
        options.addOption(Option.builder()
            .longOpt("connect-timeout")
            .hasArg()
            .type(Number.class)
            .desc("连接超时时间(秒) (默认: 30)")
            .build());
        
        options.addOption(Option.builder()
            .longOpt("read-timeout")
            .hasArg()
            .type(Number.class)
            .desc("读取超时时间(秒) (默认: 30)")
            .build());
        
        options.addOption(Option.builder()
            .longOpt("max-connections")
            .hasArg()
            .type(Number.class)
            .desc("最大总连接数 (默认: 1000)")
            .build());
        
        options.addOption(Option.builder()
            .longOpt("max-connections-per-host")
            .hasArg()
            .type(Number.class)
            .desc("每个主机的最大连接数 (默认: 200)")
            .build());
        
        options.addOption(Option.builder()
            .longOpt("no-keepalive")
            .desc("禁用Keep-Alive连接")
            .build());
        
        // 报告选项
        options.addOption(Option.builder()
            .longOpt("report-interval")
            .hasArg()
            .type(Number.class)
            .desc("报告间隔(秒) (默认: 5)")
            .build());
        
        options.addOption(Option.builder("o")
            .longOpt("output")
            .hasArg()
            .desc("输出文件路径 (CSV格式)")
            .build());
        
        options.addOption(Option.builder()
            .longOpt("no-detailed-metrics")
            .desc("禁用详细指标显示")
            .build());
        
        // 帮助和版本
        options.addOption(Option.builder("h")
            .longOpt("help")
            .desc("显示帮助信息")
            .build());
        
        options.addOption(Option.builder("v")
            .longOpt("version")
            .desc("显示版本信息")
            .build());
        
        return options;
    }
    
    /**
     * 打印帮助信息
     */
    private static void printHelp(Options options) {
        HelpFormatter formatter = new HelpFormatter();
        formatter.setWidth(100);
        
        System.out.println("HTTP负载测试工具 v" + VERSION);
        System.out.println("高性能HTTP负载测试工具，支持每分钟100,000+请求");
        System.out.println();
        
        formatter.printHelp("java -jar loadtest.jar", options);
        
        System.out.println();
        System.out.println("示例:");
        System.out.println("  # 基本测试");
        System.out.println("  java -jar loadtest.jar -u http://example.com");
        System.out.println();
        System.out.println("  # 高并发测试");
        System.out.println("  java -jar loadtest.jar -u http://example.com -r 2000 -c 200 -d 300");
        System.out.println();
        System.out.println("  # POST请求测试");
        System.out.println("  java -jar loadtest.jar -u http://api.example.com/users \\");
        System.out.println("    -m POST -H \"Content-Type: application/json\" \\");
        System.out.println("    -b '{\"name\":\"test\"}' -r 1500 -c 150");
        System.out.println();
        System.out.println("  # 保存结果到文件");
        System.out.println("  java -jar loadtest.jar -u http://example.com -o results.csv");
    }
}
