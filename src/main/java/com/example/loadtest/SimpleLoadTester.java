package com.example.loadtest;

import java.io.*;
import java.net.*;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.*;

/**
 * 简化版HTTP负载测试工具
 * 仅使用Java标准库，无外部依赖
 */
public class SimpleLoadTester {
    
    private final String targetUrl;
    private final String httpMethod;
    private final int requestsPerSecond;
    private final int totalRequests;
    private final int durationSeconds;
    private final int concurrency;
    private final Map<String, String> headers;
    private final String requestBody;
    
    // 统计数据
    private final AtomicLong totalRequestsSent = new AtomicLong(0);
    private final AtomicLong successfulRequests = new AtomicLong(0);
    private final AtomicLong failedRequests = new AtomicLong(0);
    private final AtomicLong totalResponseTime = new AtomicLong(0);
    private final AtomicLong totalBytes = new AtomicLong(0);
    private final List<Long> responseTimes = Collections.synchronizedList(new ArrayList<>());
    
    // 控制变量
    private volatile boolean running = false;
    private final long startTime;
    
    public SimpleLoadTester(String targetUrl, String httpMethod, int requestsPerSecond, 
                           int totalRequests, int durationSeconds, int concurrency,
                           Map<String, String> headers, String requestBody) {
        this.targetUrl = targetUrl;
        this.httpMethod = httpMethod.toUpperCase();
        this.requestsPerSecond = requestsPerSecond;
        this.totalRequests = totalRequests;
        this.durationSeconds = durationSeconds;
        this.concurrency = concurrency;
        this.headers = headers != null ? headers : new HashMap<>();
        this.requestBody = requestBody != null ? requestBody : "";
        this.startTime = System.currentTimeMillis();
    }
    
    public void startTest() {
        System.out.println(repeatString("=", 80));
        System.out.println("HTTP负载测试开始");
        System.out.println(repeatString("=", 80));
        System.out.println("目标URL: " + targetUrl);
        System.out.println("HTTP方法: " + httpMethod);
        System.out.println("目标RPS: " + requestsPerSecond);
        System.out.println("并发数: " + concurrency);
        if (totalRequests > 0) {
            System.out.println("总请求数: " + totalRequests);
        } else {
            System.out.println("测试持续时间: " + durationSeconds + "秒");
        }
        System.out.println("开始时间: " + new Date());
        System.out.println(repeatString("=", 80));
        System.out.println();
        
        running = true;
        
        // 创建线程池
        ExecutorService executor = Executors.newFixedThreadPool(concurrency);
        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);
        
        // 启动统计报告
        scheduler.scheduleAtFixedRate(this::printStatistics, 5, 5, TimeUnit.SECONDS);
        
        // 计算请求间隔
        long intervalMs = 1000 / requestsPerSecond;
        
        // 启动请求发送
        scheduler.scheduleAtFixedRate(() -> {
            if (!running) return;
            
            // 检查是否达到总请求数限制
            if (totalRequests > 0 && totalRequestsSent.get() >= totalRequests) {
                stopTest();
                return;
            }
            
            executor.submit(this::sendRequest);
        }, 0, intervalMs, TimeUnit.MILLISECONDS);
        
        // 如果设置了持续时间，安排停止
        if (durationSeconds > 0) {
            scheduler.schedule(this::stopTest, durationSeconds, TimeUnit.SECONDS);
        }
        
        // 等待测试完成
        while (running) {
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        // 关闭线程池
        scheduler.shutdown();
        executor.shutdown();
        
        try {
            if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
                executor.shutdownNow();
            }
        } catch (InterruptedException e) {
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        printFinalStatistics();
    }
    
    private void sendRequest() {
        if (!running) return;
        
        long requestStart = System.currentTimeMillis();
        totalRequestsSent.incrementAndGet();
        
        try {
            URL url = new URL(targetUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            
            // 设置请求方法
            connection.setRequestMethod(httpMethod);
            
            // 设置超时
            connection.setConnectTimeout(30000);
            connection.setReadTimeout(30000);
            
            // 设置请求头
            connection.setRequestProperty("User-Agent", "SimpleLoadTester/1.0");
            for (Map.Entry<String, String> header : headers.entrySet()) {
                connection.setRequestProperty(header.getKey(), header.getValue());
            }
            
            // 如果有请求体，发送它
            if (!requestBody.isEmpty() && ("POST".equals(httpMethod) || "PUT".equals(httpMethod) || "PATCH".equals(httpMethod))) {
                connection.setDoOutput(true);
                connection.setRequestProperty("Content-Type", "application/json");
                try (OutputStream os = connection.getOutputStream()) {
                    os.write(requestBody.getBytes("UTF-8"));
                }
            }
            
            // 获取响应
            int responseCode = connection.getResponseCode();
            long responseTime = System.currentTimeMillis() - requestStart;
            
            // 读取响应体
            InputStream inputStream = responseCode >= 200 && responseCode < 300 
                ? connection.getInputStream() 
                : connection.getErrorStream();
            
            long bytesRead = 0;
            if (inputStream != null) {
                byte[] buffer = new byte[1024];
                int bytesReadThisTime;
                while ((bytesReadThisTime = inputStream.read(buffer)) != -1) {
                    bytesRead += bytesReadThisTime;
                }
                inputStream.close();
            }
            
            // 记录统计
            if (responseCode >= 200 && responseCode < 300) {
                successfulRequests.incrementAndGet();
                totalBytes.addAndGet(bytesRead);
            } else {
                failedRequests.incrementAndGet();
            }
            
            totalResponseTime.addAndGet(responseTime);
            responseTimes.add(responseTime);
            
        } catch (Exception e) {
            failedRequests.incrementAndGet();
            long responseTime = System.currentTimeMillis() - requestStart;
            totalResponseTime.addAndGet(responseTime);
        }
    }
    
    private void stopTest() {
        running = false;
    }
    
    private void printStatistics() {
        long currentTime = System.currentTimeMillis();
        long elapsedTime = (currentTime - startTime) / 1000;
        long total = totalRequestsSent.get();
        long successful = successfulRequests.get();
        long failed = failedRequests.get();
        
        double currentRps = total > 0 ? (total * 1000.0) / (currentTime - startTime) : 0;
        double successRate = total > 0 ? (successful * 100.0) / total : 0;
        double avgResponseTime = total > 0 ? totalResponseTime.get() / (double) total : 0;
        
        System.out.printf("[%s] 运行时间: %ds | 总请求: %d | 成功: %d | 失败: %d | 平均RPS: %.1f%n",
            new Date(), elapsedTime, total, successful, failed, currentRps);
        System.out.printf("    成功率: %.2f%% | 平均响应时间: %.1fms | 数据传输: %.2fKB%n",
            successRate, avgResponseTime, totalBytes.get() / 1024.0);
        System.out.println();
    }
    
    private void printFinalStatistics() {
        System.out.println();
        System.out.println(repeatString("=", 80));
        System.out.println("负载测试完成 - 最终统计报告");
        System.out.println(repeatString("=", 80));
        
        long totalTime = System.currentTimeMillis() - startTime;
        long total = totalRequestsSent.get();
        long successful = successfulRequests.get();
        long failed = failedRequests.get();
        
        double overallRps = total > 0 ? (total * 1000.0) / totalTime : 0;
        double successRate = total > 0 ? (successful * 100.0) / total : 0;
        double avgResponseTime = total > 0 ? totalResponseTime.get() / (double) total : 0;
        
        System.out.println("测试概要:");
        System.out.printf("  总运行时间: %.1f秒%n", totalTime / 1000.0);
        System.out.printf("  总请求数: %d%n", total);
        System.out.printf("  成功请求: %d%n", successful);
        System.out.printf("  失败请求: %d%n", failed);
        System.out.printf("  成功率: %.2f%%%n", successRate);
        System.out.printf("  平均RPS: %.2f%n", overallRps);
        System.out.printf("  平均响应时间: %.1fms%n", avgResponseTime);
        System.out.printf("  数据传输总量: %.2fKB%n", totalBytes.get() / 1024.0);
        
        // 计算响应时间百分位数
        if (!responseTimes.isEmpty()) {
            List<Long> sortedTimes = new ArrayList<>(responseTimes);
            Collections.sort(sortedTimes);
            
            System.out.println();
            System.out.println("响应时间统计:");
            System.out.printf("  最小值: %dms%n", sortedTimes.get(0));
            System.out.printf("  最大值: %dms%n", sortedTimes.get(sortedTimes.size() - 1));
            System.out.printf("  P50 (中位数): %dms%n", getPercentile(sortedTimes, 50));
            System.out.printf("  P90: %dms%n", getPercentile(sortedTimes, 90));
            System.out.printf("  P95: %dms%n", getPercentile(sortedTimes, 95));
            System.out.printf("  P99: %dms%n", getPercentile(sortedTimes, 99));
        }
        
        System.out.println(repeatString("=", 80));
    }
    
    private long getPercentile(List<Long> sortedList, double percentile) {
        if (sortedList.isEmpty()) return 0;
        int index = (int) Math.ceil(percentile / 100.0 * sortedList.size()) - 1;
        index = Math.max(0, Math.min(index, sortedList.size() - 1));
        return sortedList.get(index);
    }
    
    private String repeatString(String str, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }
    
    public static void main(String[] args) {
        if (args.length == 0) {
            printUsage();
            return;
        }
        
        // 解析命令行参数
        String url = null;
        String method = "GET";
        int rps = 100;
        int requests = -1;
        int duration = 60;
        int concurrency = 10;
        Map<String, String> headers = new HashMap<>();
        String body = "";
        
        for (int i = 0; i < args.length; i++) {
            switch (args[i]) {
                case "-u":
                case "--url":
                    if (i + 1 < args.length) url = args[++i];
                    break;
                case "-m":
                case "--method":
                    if (i + 1 < args.length) method = args[++i];
                    break;
                case "-r":
                case "--rps":
                    if (i + 1 < args.length) rps = Integer.parseInt(args[++i]);
                    break;
                case "-n":
                case "--requests":
                    if (i + 1 < args.length) requests = Integer.parseInt(args[++i]);
                    break;
                case "-d":
                case "--duration":
                    if (i + 1 < args.length) duration = Integer.parseInt(args[++i]);
                    break;
                case "-c":
                case "--concurrency":
                    if (i + 1 < args.length) concurrency = Integer.parseInt(args[++i]);
                    break;
                case "-H":
                case "--header":
                    if (i + 1 < args.length) {
                        String header = args[++i];
                        String[] parts = header.split(":", 2);
                        if (parts.length == 2) {
                            headers.put(parts[0].trim(), parts[1].trim());
                        }
                    }
                    break;
                case "-b":
                case "--body":
                    if (i + 1 < args.length) body = args[++i];
                    break;
                case "-h":
                case "--help":
                    printUsage();
                    return;
            }
        }
        
        if (url == null) {
            System.err.println("错误: 必须指定目标URL");
            printUsage();
            return;
        }
        
        try {
            SimpleLoadTester tester = new SimpleLoadTester(url, method, rps, requests, duration, concurrency, headers, body);
            tester.startTest();
        } catch (Exception e) {
            System.err.println("错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void printUsage() {
        System.out.println("简化版HTTP负载测试工具");
        System.out.println();
        System.out.println("用法: java SimpleLoadTester [选项]");
        System.out.println();
        System.out.println("选项:");
        System.out.println("  -u, --url <URL>           目标URL (必需)");
        System.out.println("  -m, --method <METHOD>     HTTP方法 (默认: GET)");
        System.out.println("  -r, --rps <NUMBER>        每秒请求数 (默认: 100)");
        System.out.println("  -n, --requests <NUMBER>   总请求数");
        System.out.println("  -d, --duration <SECONDS>  测试持续时间 (默认: 60)");
        System.out.println("  -c, --concurrency <NUMBER> 并发数 (默认: 10)");
        System.out.println("  -H, --header <HEADER>     添加HTTP头部 (格式: 'Key: Value')");
        System.out.println("  -b, --body <BODY>         请求体内容");
        System.out.println("  -h, --help                显示帮助信息");
        System.out.println();
        System.out.println("示例:");
        System.out.println("  java SimpleLoadTester -u http://example.com");
        System.out.println("  java SimpleLoadTester -u http://example.com -r 200 -c 20 -d 120");
        System.out.println("  java SimpleLoadTester -u http://api.example.com/users -m POST -H \"Content-Type: application/json\" -b '{\"name\":\"test\"}'");
    }
}
