package com.example.loadtest;

import java.util.HashMap;
import java.util.Map;

/**
 * 负载测试配置类
 * 包含所有测试参数和配置选项
 */
public class LoadTestConfig {
    
    // 基本配置
    private String targetUrl;
    private String httpMethod = "GET";
    private int requestsPerSecond = 1000;
    private int totalRequests = -1; // -1 表示无限制
    private int durationSeconds = 60;
    private int concurrency = 100;
    
    // HTTP配置
    private Map<String, String> headers = new HashMap<>();
    private String requestBody = "";
    private String contentType = "application/json";
    
    // 连接配置
    private int connectionTimeout = 30; // 秒
    private int readTimeout = 30; // 秒
    private int maxConnectionsPerHost = 200;
    private int maxTotalConnections = 1000;
    private boolean keepAlive = true;
    
    // 报告配置
    private int reportIntervalSeconds = 5;
    private boolean enableDetailedMetrics = true;
    private String outputFile = null;
    
    // 构造函数
    public LoadTestConfig() {
        // 设置默认headers
        headers.put("User-Agent", "LoadTester/1.0");
        headers.put("Accept", "*/*");
    }
    
    // Getters and Setters
    public String getTargetUrl() {
        return targetUrl;
    }
    
    public void setTargetUrl(String targetUrl) {
        this.targetUrl = targetUrl;
    }
    
    public String getHttpMethod() {
        return httpMethod;
    }
    
    public void setHttpMethod(String httpMethod) {
        this.httpMethod = httpMethod.toUpperCase();
    }
    
    public int getRequestsPerSecond() {
        return requestsPerSecond;
    }
    
    public void setRequestsPerSecond(int requestsPerSecond) {
        this.requestsPerSecond = requestsPerSecond;
    }
    
    public int getTotalRequests() {
        return totalRequests;
    }
    
    public void setTotalRequests(int totalRequests) {
        this.totalRequests = totalRequests;
    }
    
    public int getDurationSeconds() {
        return durationSeconds;
    }
    
    public void setDurationSeconds(int durationSeconds) {
        this.durationSeconds = durationSeconds;
    }
    
    public int getConcurrency() {
        return concurrency;
    }
    
    public void setConcurrency(int concurrency) {
        this.concurrency = concurrency;
    }
    
    public Map<String, String> getHeaders() {
        return headers;
    }
    
    public void setHeaders(Map<String, String> headers) {
        this.headers = headers;
    }
    
    public void addHeader(String key, String value) {
        this.headers.put(key, value);
    }
    
    public String getRequestBody() {
        return requestBody;
    }
    
    public void setRequestBody(String requestBody) {
        this.requestBody = requestBody;
    }
    
    public String getContentType() {
        return contentType;
    }
    
    public void setContentType(String contentType) {
        this.contentType = contentType;
    }
    
    public int getConnectionTimeout() {
        return connectionTimeout;
    }
    
    public void setConnectionTimeout(int connectionTimeout) {
        this.connectionTimeout = connectionTimeout;
    }
    
    public int getReadTimeout() {
        return readTimeout;
    }
    
    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }
    
    public int getMaxConnectionsPerHost() {
        return maxConnectionsPerHost;
    }
    
    public void setMaxConnectionsPerHost(int maxConnectionsPerHost) {
        this.maxConnectionsPerHost = maxConnectionsPerHost;
    }
    
    public int getMaxTotalConnections() {
        return maxTotalConnections;
    }
    
    public void setMaxTotalConnections(int maxTotalConnections) {
        this.maxTotalConnections = maxTotalConnections;
    }
    
    public boolean isKeepAlive() {
        return keepAlive;
    }
    
    public void setKeepAlive(boolean keepAlive) {
        this.keepAlive = keepAlive;
    }
    
    public int getReportIntervalSeconds() {
        return reportIntervalSeconds;
    }
    
    public void setReportIntervalSeconds(int reportIntervalSeconds) {
        this.reportIntervalSeconds = reportIntervalSeconds;
    }
    
    public boolean isEnableDetailedMetrics() {
        return enableDetailedMetrics;
    }
    
    public void setEnableDetailedMetrics(boolean enableDetailedMetrics) {
        this.enableDetailedMetrics = enableDetailedMetrics;
    }
    
    public String getOutputFile() {
        return outputFile;
    }
    
    public void setOutputFile(String outputFile) {
        this.outputFile = outputFile;
    }
    
    /**
     * 验证配置的有效性
     */
    public void validate() {
        if (targetUrl == null || targetUrl.trim().isEmpty()) {
            throw new IllegalArgumentException("目标URL不能为空");
        }
        
        if (!targetUrl.startsWith("http://") && !targetUrl.startsWith("https://")) {
            throw new IllegalArgumentException("目标URL必须以http://或https://开头");
        }
        
        if (requestsPerSecond <= 0) {
            throw new IllegalArgumentException("每秒请求数必须大于0");
        }
        
        if (concurrency <= 0) {
            throw new IllegalArgumentException("并发数必须大于0");
        }
        
        if (durationSeconds <= 0 && totalRequests <= 0) {
            throw new IllegalArgumentException("必须指定测试持续时间或总请求数");
        }
    }
    
    @Override
    public String toString() {
        return String.format(
            "LoadTestConfig{targetUrl='%s', method='%s', rps=%d, concurrency=%d, duration=%ds, totalRequests=%d}",
            targetUrl, httpMethod, requestsPerSecond, concurrency, durationSeconds, totalRequests
        );
    }
}
