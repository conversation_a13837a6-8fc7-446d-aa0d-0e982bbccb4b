package com.example.loadtest;

import org.HdrHistogram.Histogram;

import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 统计数据收集器
 * 收集和管理负载测试的各种统计指标
 */
public class StatisticsCollector {
    
    // 基本计数器
    private final AtomicLong totalRequests = new AtomicLong(0);
    private final AtomicLong successfulRequests = new AtomicLong(0);
    private final AtomicLong failedRequests = new AtomicLong(0);
    private final AtomicLong totalBytes = new AtomicLong(0);
    
    // 响应时间统计 (使用HdrHistogram进行高精度统计)
    private final Histogram responseTimeHistogram;
    
    // 错误统计
    private final AtomicLong connectionErrors = new AtomicLong(0);
    private final AtomicLong timeoutErrors = new AtomicLong(0);
    private final AtomicLong httpErrors = new AtomicLong(0);
    private final AtomicLong otherErrors = new AtomicLong(0);
    
    // 时间记录
    private final long startTime;
    private final AtomicReference<Long> endTime = new AtomicReference<>();
    
    // 实时统计
    private final AtomicLong lastReportTime = new AtomicLong();
    private final AtomicLong lastRequestCount = new AtomicLong(0);
    
    public StatisticsCollector() {
        this.startTime = System.currentTimeMillis();
        this.lastReportTime.set(startTime);
        
        // 创建响应时间直方图，支持1微秒到1分钟的响应时间，3位有效数字
        this.responseTimeHistogram = new Histogram(1, 60_000_000L, 3);
    }
    
    /**
     * 记录成功的请求
     */
    public void recordSuccess(long responseTimeMs, long responseBytes) {
        totalRequests.incrementAndGet();
        successfulRequests.incrementAndGet();
        totalBytes.addAndGet(responseBytes);
        
        // 记录响应时间（转换为微秒）
        responseTimeHistogram.recordValue(responseTimeMs * 1000);
    }
    
    /**
     * 记录失败的请求
     */
    public void recordFailure(String errorType, long responseTimeMs) {
        totalRequests.incrementAndGet();
        failedRequests.incrementAndGet();
        
        if (responseTimeMs > 0) {
            responseTimeHistogram.recordValue(responseTimeMs * 1000);
        }
        
        // 分类错误类型
        switch (errorType.toLowerCase()) {
            case "connection":
                connectionErrors.incrementAndGet();
                break;
            case "timeout":
                timeoutErrors.incrementAndGet();
                break;
            case "http":
                httpErrors.incrementAndGet();
                break;
            default:
                otherErrors.incrementAndGet();
                break;
        }
    }
    
    /**
     * 标记测试结束
     */
    public void markTestEnd() {
        endTime.set(System.currentTimeMillis());
    }
    
    /**
     * 获取当前统计快照
     */
    public StatisticsSnapshot getSnapshot() {
        long currentTime = System.currentTimeMillis();
        long currentRequests = totalRequests.get();
        long lastReportTimeValue = lastReportTime.get();
        long lastRequestCountValue = lastRequestCount.get();
        
        // 计算当前RPS
        double currentRps = 0.0;
        long timeDiff = currentTime - lastReportTimeValue;
        if (timeDiff > 0) {
            long requestDiff = currentRequests - lastRequestCountValue;
            currentRps = (requestDiff * 1000.0) / timeDiff;
        }
        
        // 更新上次报告时间和请求数
        lastReportTime.set(currentTime);
        lastRequestCount.set(currentRequests);
        
        return new StatisticsSnapshot(
            currentTime,
            currentRequests,
            successfulRequests.get(),
            failedRequests.get(),
            totalBytes.get(),
            responseTimeHistogram.copy(),
            connectionErrors.get(),
            timeoutErrors.get(),
            httpErrors.get(),
            otherErrors.get(),
            startTime,
            endTime.get(),
            currentRps
        );
    }
    
    /**
     * 重置统计数据
     */
    public void reset() {
        totalRequests.set(0);
        successfulRequests.set(0);
        failedRequests.set(0);
        totalBytes.set(0);
        connectionErrors.set(0);
        timeoutErrors.set(0);
        httpErrors.set(0);
        otherErrors.set(0);
        responseTimeHistogram.reset();
        lastRequestCount.set(0);
        lastReportTime.set(System.currentTimeMillis());
    }
    
    // Getters
    public long getTotalRequests() {
        return totalRequests.get();
    }
    
    public long getSuccessfulRequests() {
        return successfulRequests.get();
    }
    
    public long getFailedRequests() {
        return failedRequests.get();
    }
    
    public long getTotalBytes() {
        return totalBytes.get();
    }
    
    public long getStartTime() {
        return startTime;
    }
    
    public Long getEndTime() {
        return endTime.get();
    }
    
    /**
     * 统计快照类
     */
    public static class StatisticsSnapshot {
        private final long timestamp;
        private final long totalRequests;
        private final long successfulRequests;
        private final long failedRequests;
        private final long totalBytes;
        private final Histogram responseTimeHistogram;
        private final long connectionErrors;
        private final long timeoutErrors;
        private final long httpErrors;
        private final long otherErrors;
        private final long startTime;
        private final Long endTime;
        private final double currentRps;
        
        public StatisticsSnapshot(long timestamp, long totalRequests, long successfulRequests,
                                long failedRequests, long totalBytes, Histogram responseTimeHistogram,
                                long connectionErrors, long timeoutErrors, long httpErrors,
                                long otherErrors, long startTime, Long endTime, double currentRps) {
            this.timestamp = timestamp;
            this.totalRequests = totalRequests;
            this.successfulRequests = successfulRequests;
            this.failedRequests = failedRequests;
            this.totalBytes = totalBytes;
            this.responseTimeHistogram = responseTimeHistogram;
            this.connectionErrors = connectionErrors;
            this.timeoutErrors = timeoutErrors;
            this.httpErrors = httpErrors;
            this.otherErrors = otherErrors;
            this.startTime = startTime;
            this.endTime = endTime;
            this.currentRps = currentRps;
        }
        
        // Getters
        public long getTimestamp() { return timestamp; }
        public long getTotalRequests() { return totalRequests; }
        public long getSuccessfulRequests() { return successfulRequests; }
        public long getFailedRequests() { return failedRequests; }
        public long getTotalBytes() { return totalBytes; }
        public Histogram getResponseTimeHistogram() { return responseTimeHistogram; }
        public long getConnectionErrors() { return connectionErrors; }
        public long getTimeoutErrors() { return timeoutErrors; }
        public long getHttpErrors() { return httpErrors; }
        public long getOtherErrors() { return otherErrors; }
        public long getStartTime() { return startTime; }
        public Long getEndTime() { return endTime; }
        public double getCurrentRps() { return currentRps; }
        
        /**
         * 计算总体RPS
         */
        public double getOverallRps() {
            long duration = (endTime != null ? endTime : timestamp) - startTime;
            return duration > 0 ? (totalRequests * 1000.0) / duration : 0.0;
        }
        
        /**
         * 计算成功率
         */
        public double getSuccessRate() {
            return totalRequests > 0 ? (successfulRequests * 100.0) / totalRequests : 0.0;
        }
        
        /**
         * 获取响应时间统计（毫秒）
         */
        public double getMinResponseTime() {
            return responseTimeHistogram.getMinValue() / 1000.0;
        }
        
        public double getMaxResponseTime() {
            return responseTimeHistogram.getMaxValue() / 1000.0;
        }
        
        public double getMeanResponseTime() {
            return responseTimeHistogram.getMean() / 1000.0;
        }
        
        public double getPercentile(double percentile) {
            return responseTimeHistogram.getValueAtPercentile(percentile) / 1000.0;
        }
    }
}
