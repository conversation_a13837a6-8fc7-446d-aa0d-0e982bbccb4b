package com.example.loadtest;

import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 指标报告器
 * 负责实时显示和记录负载测试的统计信息
 */
public class MetricsReporter {
    
    private final StatisticsCollector statisticsCollector;
    private final AsyncHttpClient httpClient;
    private final LoadTestConfig config;
    private final ScheduledExecutorService scheduler;
    private final SimpleDateFormat dateFormat;
    private PrintWriter fileWriter;
    
    public MetricsReporter(StatisticsCollector statisticsCollector, 
                          AsyncHttpClient httpClient, 
                          LoadTestConfig config) {
        this.statisticsCollector = statisticsCollector;
        this.httpClient = httpClient;
        this.config = config;
        this.scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "MetricsReporter");
            t.setDaemon(true);
            return t;
        });
        this.dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        
        // 如果指定了输出文件，创建文件写入器
        if (config.getOutputFile() != null) {
            try {
                fileWriter = new PrintWriter(new FileWriter(config.getOutputFile(), true));
                writeFileHeader();
            } catch (IOException e) {
                System.err.println("无法创建输出文件: " + e.getMessage());
            }
        }
    }
    
    /**
     * 开始定期报告
     */
    public void startReporting() {
        // 立即显示初始报告
        printInitialReport();
        
        // 定期报告
        scheduler.scheduleAtFixedRate(
            this::printPeriodicReport,
            config.getReportIntervalSeconds(),
            config.getReportIntervalSeconds(),
            TimeUnit.SECONDS
        );
    }
    
    /**
     * 停止报告并显示最终统计
     */
    public void stopReporting() {
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(2, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        printFinalReport();
        
        if (fileWriter != null) {
            fileWriter.close();
        }
    }
    
    /**
     * 打印初始报告
     */
    private void printInitialReport() {
        System.out.println("=".repeat(80));
        System.out.println("HTTP负载测试开始");
        System.out.println("=".repeat(80));
        System.out.println("目标URL: " + config.getTargetUrl());
        System.out.println("HTTP方法: " + config.getHttpMethod());
        System.out.println("目标RPS: " + config.getRequestsPerSecond());
        System.out.println("并发数: " + config.getConcurrency());
        System.out.println("测试持续时间: " + config.getDurationSeconds() + "秒");
        if (config.getTotalRequests() > 0) {
            System.out.println("总请求数: " + config.getTotalRequests());
        }
        System.out.println("开始时间: " + dateFormat.format(new Date()));
        System.out.println("=".repeat(80));
        System.out.println();
    }
    
    /**
     * 打印定期报告
     */
    private void printPeriodicReport() {
        StatisticsCollector.StatisticsSnapshot snapshot = statisticsCollector.getSnapshot();
        AsyncHttpClient.ConnectionPoolStats poolStats = httpClient.getConnectionPoolStats();
        
        long elapsedTime = (snapshot.getTimestamp() - snapshot.getStartTime()) / 1000;
        
        System.out.printf("[%s] 运行时间: %ds | ", 
            dateFormat.format(new Date(snapshot.getTimestamp())), elapsedTime);
        System.out.printf("总请求: %d | 成功: %d | 失败: %d | ",
            snapshot.getTotalRequests(), snapshot.getSuccessfulRequests(), snapshot.getFailedRequests());
        System.out.printf("当前RPS: %.1f | 平均RPS: %.1f%n",
            snapshot.getCurrentRps(), snapshot.getOverallRps());
        
        if (config.isEnableDetailedMetrics()) {
            System.out.printf("    响应时间 - 最小: %.1fms | 平均: %.1fms | 最大: %.1fms | ",
                snapshot.getMinResponseTime(), snapshot.getMeanResponseTime(), snapshot.getMaxResponseTime());
            System.out.printf("P95: %.1fms | P99: %.1fms%n",
                snapshot.getPercentile(95), snapshot.getPercentile(99));
            System.out.printf("    成功率: %.2f%% | 数据传输: %.2fMB | %s%n",
                snapshot.getSuccessRate(), snapshot.getTotalBytes() / 1024.0 / 1024.0, poolStats);
            
            if (snapshot.getFailedRequests() > 0) {
                System.out.printf("    错误统计 - 连接: %d | 超时: %d | HTTP: %d | 其他: %d%n",
                    snapshot.getConnectionErrors(), snapshot.getTimeoutErrors(),
                    snapshot.getHttpErrors(), snapshot.getOtherErrors());
            }
        }
        
        // 写入文件
        if (fileWriter != null) {
            writeToFile(snapshot, poolStats, elapsedTime);
        }
        
        System.out.println();
    }
    
    /**
     * 打印最终报告
     */
    private void printFinalReport() {
        statisticsCollector.markTestEnd();
        StatisticsCollector.StatisticsSnapshot finalSnapshot = statisticsCollector.getSnapshot();
        
        System.out.println();
        System.out.println("=".repeat(80));
        System.out.println("负载测试完成 - 最终统计报告");
        System.out.println("=".repeat(80));
        
        long totalDuration = (finalSnapshot.getEndTime() - finalSnapshot.getStartTime()) / 1000;
        
        System.out.println("测试概要:");
        System.out.printf("  总运行时间: %d秒%n", totalDuration);
        System.out.printf("  总请求数: %d%n", finalSnapshot.getTotalRequests());
        System.out.printf("  成功请求: %d%n", finalSnapshot.getSuccessfulRequests());
        System.out.printf("  失败请求: %d%n", finalSnapshot.getFailedRequests());
        System.out.printf("  成功率: %.2f%%%n", finalSnapshot.getSuccessRate());
        System.out.printf("  平均RPS: %.2f%n", finalSnapshot.getOverallRps());
        System.out.printf("  数据传输总量: %.2fMB%n", finalSnapshot.getTotalBytes() / 1024.0 / 1024.0);
        
        System.out.println();
        System.out.println("响应时间统计:");
        System.out.printf("  最小值: %.1fms%n", finalSnapshot.getMinResponseTime());
        System.out.printf("  最大值: %.1fms%n", finalSnapshot.getMaxResponseTime());
        System.out.printf("  平均值: %.1fms%n", finalSnapshot.getMeanResponseTime());
        System.out.printf("  P50 (中位数): %.1fms%n", finalSnapshot.getPercentile(50));
        System.out.printf("  P90: %.1fms%n", finalSnapshot.getPercentile(90));
        System.out.printf("  P95: %.1fms%n", finalSnapshot.getPercentile(95));
        System.out.printf("  P99: %.1fms%n", finalSnapshot.getPercentile(99));
        System.out.printf("  P99.9: %.1fms%n", finalSnapshot.getPercentile(99.9));
        
        if (finalSnapshot.getFailedRequests() > 0) {
            System.out.println();
            System.out.println("错误统计:");
            System.out.printf("  连接错误: %d%n", finalSnapshot.getConnectionErrors());
            System.out.printf("  超时错误: %d%n", finalSnapshot.getTimeoutErrors());
            System.out.printf("  HTTP错误: %d%n", finalSnapshot.getHttpErrors());
            System.out.printf("  其他错误: %d%n", finalSnapshot.getOtherErrors());
        }
        
        System.out.println("=".repeat(80));
        
        if (config.getOutputFile() != null) {
            System.out.println("详细统计数据已保存到: " + config.getOutputFile());
        }
    }
    
    /**
     * 写入文件头部
     */
    private void writeFileHeader() {
        if (fileWriter != null) {
            fileWriter.println("timestamp,elapsed_time,total_requests,successful_requests,failed_requests," +
                "current_rps,overall_rps,success_rate,min_response_time,mean_response_time,max_response_time," +
                "p95_response_time,p99_response_time,total_bytes,active_connections");
            fileWriter.flush();
        }
    }
    
    /**
     * 写入统计数据到文件
     */
    private void writeToFile(StatisticsCollector.StatisticsSnapshot snapshot, 
                           AsyncHttpClient.ConnectionPoolStats poolStats, 
                           long elapsedTime) {
        if (fileWriter != null) {
            fileWriter.printf("%s,%d,%d,%d,%d,%.2f,%.2f,%.2f,%.1f,%.1f,%.1f,%.1f,%.1f,%d,%d%n",
                dateFormat.format(new Date(snapshot.getTimestamp())),
                elapsedTime,
                snapshot.getTotalRequests(),
                snapshot.getSuccessfulRequests(),
                snapshot.getFailedRequests(),
                snapshot.getCurrentRps(),
                snapshot.getOverallRps(),
                snapshot.getSuccessRate(),
                snapshot.getMinResponseTime(),
                snapshot.getMeanResponseTime(),
                snapshot.getMaxResponseTime(),
                snapshot.getPercentile(95),
                snapshot.getPercentile(99),
                snapshot.getTotalBytes(),
                poolStats.getActiveConnections()
            );
            fileWriter.flush();
        }
    }
}
