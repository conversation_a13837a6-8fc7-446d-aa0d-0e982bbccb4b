package com.example.loadtest;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 负载测试执行器
 * 控制请求的发送速率和并发执行
 */
public class LoadTestExecutor {
    
    private final LoadTestConfig config;
    private final AsyncHttpClient httpClient;
    private final StatisticsCollector statisticsCollector;
    private final MetricsReporter metricsReporter;
    
    private final ScheduledExecutorService scheduler;
    private final ExecutorService requestExecutor;
    private final AtomicBoolean running = new AtomicBoolean(false);
    private final AtomicLong requestsSent = new AtomicLong(0);
    
    // 速率控制
    private final Semaphore rateLimiter;
    private final long intervalNanos;
    
    public LoadTestExecutor(LoadTestConfig config, 
                           AsyncHttpClient httpClient, 
                           StatisticsCollector statisticsCollector,
                           MetricsReporter metricsReporter) {
        this.config = config;
        this.httpClient = httpClient;
        this.statisticsCollector = statisticsCollector;
        this.metricsReporter = metricsReporter;
        
        // 创建线程池
        this.scheduler = Executors.newScheduledThreadPool(2, r -> {
            Thread t = new Thread(r, "LoadTestScheduler");
            t.setDaemon(true);
            return t;
        });
        
        this.requestExecutor = Executors.newFixedThreadPool(config.getConcurrency(), r -> {
            Thread t = new Thread(r, "RequestWorker");
            t.setDaemon(true);
            return t;
        });
        
        // 初始化速率控制
        this.rateLimiter = new Semaphore(config.getRequestsPerSecond());
        this.intervalNanos = TimeUnit.SECONDS.toNanos(1) / config.getRequestsPerSecond();
    }
    
    /**
     * 开始负载测试
     */
    public void startTest() {
        if (!running.compareAndSet(false, true)) {
            throw new IllegalStateException("测试已经在运行中");
        }
        
        System.out.println("正在启动负载测试...");
        
        // 开始指标报告
        metricsReporter.startReporting();
        
        // 启动速率控制器
        startRateController();
        
        // 启动请求发送器
        startRequestSender();
        
        // 如果设置了测试持续时间，安排停止任务
        if (config.getDurationSeconds() > 0) {
            scheduler.schedule(this::stopTest, config.getDurationSeconds(), TimeUnit.SECONDS);
        }
        
        System.out.println("负载测试已启动");
    }
    
    /**
     * 停止负载测试
     */
    public void stopTest() {
        if (!running.compareAndSet(true, false)) {
            return; // 已经停止
        }
        
        System.out.println("正在停止负载测试...");
        
        // 停止调度器
        scheduler.shutdown();
        
        // 停止请求执行器
        requestExecutor.shutdown();
        
        try {
            // 等待正在执行的请求完成
            if (!requestExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                requestExecutor.shutdownNow();
                if (!requestExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    System.err.println("请求执行器无法正常关闭");
                }
            }
        } catch (InterruptedException e) {
            requestExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        // 停止指标报告
        metricsReporter.stopReporting();
        
        System.out.println("负载测试已停止");
    }
    
    /**
     * 等待测试完成
     */
    public void waitForCompletion() {
        while (running.get()) {
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }
    
    /**
     * 启动速率控制器
     */
    private void startRateController() {
        scheduler.scheduleAtFixedRate(() -> {
            if (!running.get()) {
                return;
            }
            
            // 每秒补充许可证
            int permits = config.getRequestsPerSecond() - rateLimiter.availablePermits();
            if (permits > 0) {
                rateLimiter.release(permits);
            }
        }, 1, 1, TimeUnit.SECONDS);
    }
    
    /**
     * 启动请求发送器
     */
    private void startRequestSender() {
        // 使用固定间隔发送请求以实现精确的速率控制
        long initialDelay = 0;
        scheduler.scheduleAtFixedRate(() -> {
            if (!running.get()) {
                return;
            }
            
            // 检查是否达到总请求数限制
            if (config.getTotalRequests() > 0 && 
                requestsSent.get() >= config.getTotalRequests()) {
                stopTest();
                return;
            }
            
            // 尝试获取速率限制许可
            if (rateLimiter.tryAcquire()) {
                requestExecutor.submit(this::sendSingleRequest);
            }
        }, initialDelay, intervalNanos, TimeUnit.NANOSECONDS);
    }
    
    /**
     * 发送单个请求
     */
    private void sendSingleRequest() {
        if (!running.get()) {
            return;
        }
        
        try {
            requestsSent.incrementAndGet();
            httpClient.sendAsyncRequest(null); // 使用默认回调
        } catch (Exception e) {
            // 记录发送请求时的异常
            statisticsCollector.recordFailure("other", 0);
            System.err.println("发送请求时发生异常: " + e.getMessage());
        }
    }
    
    /**
     * 优雅关闭
     */
    public void shutdown() {
        stopTest();
        
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        // 关闭HTTP客户端
        httpClient.shutdown();
    }
    
    /**
     * 检查测试是否正在运行
     */
    public boolean isRunning() {
        return running.get();
    }
    
    /**
     * 获取已发送的请求数
     */
    public long getRequestsSent() {
        return requestsSent.get();
    }
    
    /**
     * 获取当前速率限制器状态
     */
    public RateLimiterStats getRateLimiterStats() {
        return new RateLimiterStats(
            rateLimiter.availablePermits(),
            rateLimiter.getQueueLength()
        );
    }
    
    /**
     * 速率限制器统计信息
     */
    public static class RateLimiterStats {
        private final int availablePermits;
        private final int queueLength;
        
        public RateLimiterStats(int availablePermits, int queueLength) {
            this.availablePermits = availablePermits;
            this.queueLength = queueLength;
        }
        
        public int getAvailablePermits() {
            return availablePermits;
        }
        
        public int getQueueLength() {
            return queueLength;
        }
        
        @Override
        public String toString() {
            return String.format("速率限制器: 可用许可=%d, 队列长度=%d", 
                availablePermits, queueLength);
        }
    }
}
