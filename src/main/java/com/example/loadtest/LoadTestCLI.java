package com.example.loadtest;

/**
 * 负载测试命令行工具
 */
public class LoadTestCLI {
    
    public static void main(String[] args) {
        if (args.length == 0) {
            printUsage();
            return;
        }
        
        try {
            StandardLoadTester.Builder builder = parseArguments(args);
            StandardLoadTester tester = builder.build();
            
            // 注册关闭钩子
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                System.out.println("\n收到关闭信号，正在停止测试...");
            }));
            
            tester.startTest();
            
        } catch (Exception e) {
            System.err.println("错误: " + e.getMessage());
            System.exit(1);
        }
    }
    
    private static StandardLoadTester.Builder parseArguments(String[] args) {
        StandardLoadTester.Builder builder = new StandardLoadTester.Builder();
        
        for (int i = 0; i < args.length; i++) {
            String arg = args[i];
            
            switch (arg) {
                case "-u":
                case "--url":
                    if (i + 1 >= args.length) {
                        throw new IllegalArgumentException("缺少URL参数");
                    }
                    builder.url(args[++i]);
                    break;
                    
                case "-m":
                case "--method":
                    if (i + 1 >= args.length) {
                        throw new IllegalArgumentException("缺少HTTP方法参数");
                    }
                    builder.method(args[++i]);
                    break;
                    
                case "-r":
                case "--rps":
                    if (i + 1 >= args.length) {
                        throw new IllegalArgumentException("缺少RPS参数");
                    }
                    try {
                        builder.rps(Integer.parseInt(args[++i]));
                    } catch (NumberFormatException e) {
                        throw new IllegalArgumentException("无效的RPS值: " + args[i]);
                    }
                    break;
                    
                case "-n":
                case "--requests":
                    if (i + 1 >= args.length) {
                        throw new IllegalArgumentException("缺少请求数参数");
                    }
                    try {
                        builder.requests(Integer.parseInt(args[++i]));
                    } catch (NumberFormatException e) {
                        throw new IllegalArgumentException("无效的请求数值: " + args[i]);
                    }
                    break;
                    
                case "-d":
                case "--duration":
                    if (i + 1 >= args.length) {
                        throw new IllegalArgumentException("缺少持续时间参数");
                    }
                    try {
                        builder.duration(Integer.parseInt(args[++i]));
                    } catch (NumberFormatException e) {
                        throw new IllegalArgumentException("无效的持续时间值: " + args[i]);
                    }
                    break;
                    
                case "-c":
                case "--concurrency":
                    if (i + 1 >= args.length) {
                        throw new IllegalArgumentException("缺少并发数参数");
                    }
                    try {
                        builder.concurrency(Integer.parseInt(args[++i]));
                    } catch (NumberFormatException e) {
                        throw new IllegalArgumentException("无效的并发数值: " + args[i]);
                    }
                    break;
                    
                case "-H":
                case "--header":
                    if (i + 1 >= args.length) {
                        throw new IllegalArgumentException("缺少头部参数");
                    }
                    String header = args[++i];
                    String[] parts = header.split(":", 2);
                    if (parts.length != 2) {
                        throw new IllegalArgumentException("无效的头部格式，应为 'Key: Value': " + header);
                    }
                    builder.header(parts[0].trim(), parts[1].trim());
                    break;
                    
                case "-b":
                case "--body":
                    if (i + 1 >= args.length) {
                        throw new IllegalArgumentException("缺少请求体参数");
                    }
                    builder.body(args[++i]);
                    break;
                    
                case "--connect-timeout":
                    if (i + 1 >= args.length) {
                        throw new IllegalArgumentException("缺少连接超时参数");
                    }
                    try {
                        builder.connectionTimeout(Integer.parseInt(args[++i]) * 1000);
                    } catch (NumberFormatException e) {
                        throw new IllegalArgumentException("无效的连接超时值: " + args[i]);
                    }
                    break;
                    
                case "--read-timeout":
                    if (i + 1 >= args.length) {
                        throw new IllegalArgumentException("缺少读取超时参数");
                    }
                    try {
                        builder.readTimeout(Integer.parseInt(args[++i]) * 1000);
                    } catch (NumberFormatException e) {
                        throw new IllegalArgumentException("无效的读取超时值: " + args[i]);
                    }
                    break;
                    
                case "-h":
                case "--help":
                    printUsage();
                    System.exit(0);
                    break;
                    
                case "-v":
                case "--version":
                    System.out.println("HTTP负载测试工具 v1.0.0");
                    System.exit(0);
                    break;
                    
                default:
                    throw new IllegalArgumentException("未知参数: " + arg);
            }
        }
        
        return builder;
    }
    
    private static void printUsage() {
        System.out.println("HTTP负载测试工具 v1.0.0");
        System.out.println("高性能HTTP负载测试工具，支持每分钟100,000+请求");
        System.out.println();
        System.out.println("用法: java LoadTestCLI [选项]");
        System.out.println();
        System.out.println("基本选项:");
        System.out.println("  -u, --url <URL>              目标URL (必需)");
        System.out.println("  -m, --method <METHOD>        HTTP方法 (默认: GET)");
        System.out.println("  -r, --rps <NUMBER>           每秒请求数 (默认: 100)");
        System.out.println("  -d, --duration <SECONDS>     测试持续时间(秒) (默认: 60)");
        System.out.println("  -n, --requests <NUMBER>      总请求数 (如果指定，将忽略duration)");
        System.out.println("  -c, --concurrency <NUMBER>   并发数 (默认: 10)");
        System.out.println();
        System.out.println("HTTP选项:");
        System.out.println("  -H, --header <HEADER>        添加HTTP头部 (格式: 'Key: Value')");
        System.out.println("  -b, --body <BODY>            请求体内容");
        System.out.println();
        System.out.println("连接选项:");
        System.out.println("  --connect-timeout <SECONDS> 连接超时时间(秒) (默认: 30)");
        System.out.println("  --read-timeout <SECONDS>     读取超时时间(秒) (默认: 30)");
        System.out.println();
        System.out.println("其他选项:");
        System.out.println("  -h, --help                   显示帮助信息");
        System.out.println("  -v, --version                显示版本信息");
        System.out.println();
        System.out.println("示例:");
        System.out.println("  # 基本GET请求测试");
        System.out.println("  java LoadTestCLI -u http://example.com");
        System.out.println();
        System.out.println("  # 高并发测试");
        System.out.println("  java LoadTestCLI -u http://example.com -r 2000 -c 200 -d 300");
        System.out.println();
        System.out.println("  # POST请求测试");
        System.out.println("  java LoadTestCLI -u http://api.example.com/users \\");
        System.out.println("    -m POST -H \"Content-Type: application/json\" \\");
        System.out.println("    -b '{\"name\":\"test\"}' -r 1500 -c 150");
        System.out.println();
        System.out.println("  # 固定请求数测试");
        System.out.println("  java LoadTestCLI -u http://example.com -n 10000 -r 1000");
        System.out.println();
        System.out.println("性能建议:");
        System.out.println("  - 对于高RPS测试，建议增加并发数和JVM堆内存");
        System.out.println("  - 使用 -Xmx2g -Xms2g 等JVM参数优化内存使用");
        System.out.println("  - 确保系统文件描述符限制足够大 (ulimit -n 65536)");
    }
    
    /**
     * 运行预定义的示例测试
     */
    public static void runExamples() {
        System.out.println("运行示例测试...");
        System.out.println();
        
        // 示例1: 基本GET测试
        System.out.println("示例1: 基本GET测试");
        try {
            StandardLoadTester tester1 = new StandardLoadTester.Builder()
                .url("https://httpbin.org/get")
                .rps(10)
                .concurrency(5)
                .duration(15)
                .build();
            tester1.startTest();
        } catch (Exception e) {
            System.err.println("示例1失败: " + e.getMessage());
        }
        
        System.out.println("\n" + new String(new char[80]).replace('\0', '=') + "\n");
        
        // 示例2: POST测试
        System.out.println("示例2: POST请求测试");
        try {
            StandardLoadTester tester2 = new StandardLoadTester.Builder()
                .url("https://httpbin.org/post")
                .method("POST")
                .header("Content-Type", "application/json")
                .body("{\"test\": \"data\", \"timestamp\": " + System.currentTimeMillis() + "}")
                .rps(5)
                .concurrency(3)
                .duration(10)
                .build();
            tester2.startTest();
        } catch (Exception e) {
            System.err.println("示例2失败: " + e.getMessage());
        }
        
        System.out.println("\n所有示例测试完成！");
    }
}
