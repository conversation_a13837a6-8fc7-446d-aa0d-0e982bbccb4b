package com.example.loadtest;

import okhttp3.*;
import okhttp3.ConnectionPool;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * 异步HTTP客户端管理器
 * 基于OkHttp实现高性能异步HTTP请求
 */
public class AsyncHttpClient {
    
    private final OkHttpClient client;
    private final LoadTestConfig config;
    private final StatisticsCollector statisticsCollector;
    
    public AsyncHttpClient(LoadTestConfig config, StatisticsCollector statisticsCollector) {
        this.config = config;
        this.statisticsCollector = statisticsCollector;
        this.client = createHttpClient();
    }
    
    /**
     * 创建配置好的OkHttpClient
     */
    private OkHttpClient createHttpClient() {
        ConnectionPool connectionPool = new ConnectionPool(
            config.getMaxTotalConnections(),
            5, // 保持连接5分钟
            TimeUnit.MINUTES
        );
        
        return new OkHttpClient.Builder()
            .connectionPool(connectionPool)
            .connectTimeout(config.getConnectionTimeout(), TimeUnit.SECONDS)
            .readTimeout(config.getReadTimeout(), TimeUnit.SECONDS)
            .writeTimeout(config.getReadTimeout(), TimeUnit.SECONDS)
            .retryOnConnectionFailure(false) // 禁用自动重试以获得准确的统计
            .followRedirects(true)
            .followSslRedirects(true)
            .build();
    }
    
    /**
     * 发送异步HTTP请求
     */
    public void sendAsyncRequest(Callback callback) {
        Request request = buildRequest();
        long startTime = System.currentTimeMillis();
        
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                long responseTime = System.currentTimeMillis() - startTime;
                String errorType = determineErrorType(e);
                statisticsCollector.recordFailure(errorType, responseTime);
                
                if (callback != null) {
                    callback.onFailure(call, e);
                }
            }
            
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                long responseTime = System.currentTimeMillis() - startTime;
                
                try {
                    if (response.isSuccessful()) {
                        long responseBytes = 0;
                        ResponseBody body = response.body();
                        if (body != null) {
                            responseBytes = body.contentLength();
                            // 消费响应体以释放连接
                            body.close();
                        }
                        statisticsCollector.recordSuccess(responseTime, responseBytes);
                    } else {
                        statisticsCollector.recordFailure("http", responseTime);
                    }
                } finally {
                    response.close();
                }
                
                if (callback != null) {
                    callback.onResponse(call, response);
                }
            }
        });
    }
    
    /**
     * 构建HTTP请求
     */
    private Request buildRequest() {
        Request.Builder builder = new Request.Builder()
            .url(config.getTargetUrl());
        
        // 添加headers
        for (String key : config.getHeaders().keySet()) {
            builder.addHeader(key, config.getHeaders().get(key));
        }
        
        // 设置请求方法和body
        RequestBody requestBody = null;
        if (!config.getRequestBody().isEmpty()) {
            MediaType mediaType = MediaType.parse(config.getContentType());
            requestBody = RequestBody.create(config.getRequestBody(), mediaType);
        }
        
        switch (config.getHttpMethod()) {
            case "GET":
                builder.get();
                break;
            case "POST":
                builder.post(requestBody != null ? requestBody : RequestBody.create("", null));
                break;
            case "PUT":
                builder.put(requestBody != null ? requestBody : RequestBody.create("", null));
                break;
            case "DELETE":
                if (requestBody != null) {
                    builder.delete(requestBody);
                } else {
                    builder.delete();
                }
                break;
            case "HEAD":
                builder.head();
                break;
            case "PATCH":
                builder.patch(requestBody != null ? requestBody : RequestBody.create("", null));
                break;
            default:
                throw new IllegalArgumentException("不支持的HTTP方法: " + config.getHttpMethod());
        }
        
        return builder.build();
    }
    
    /**
     * 确定错误类型
     */
    private String determineErrorType(IOException e) {
        String message = e.getMessage();
        if (message == null) {
            return "other";
        }
        
        message = message.toLowerCase();
        if (message.contains("timeout") || message.contains("timed out")) {
            return "timeout";
        } else if (message.contains("connection") || message.contains("connect")) {
            return "connection";
        } else {
            return "other";
        }
    }
    
    /**
     * 获取连接池统计信息
     */
    public ConnectionPoolStats getConnectionPoolStats() {
        ConnectionPool pool = client.connectionPool();
        return new ConnectionPoolStats(
            pool.connectionCount(),
            pool.idleConnectionCount()
        );
    }
    
    /**
     * 关闭客户端并释放资源
     */
    public void shutdown() {
        client.dispatcher().executorService().shutdown();
        client.connectionPool().evictAll();
        
        try {
            if (!client.dispatcher().executorService().awaitTermination(5, TimeUnit.SECONDS)) {
                client.dispatcher().executorService().shutdownNow();
            }
        } catch (InterruptedException e) {
            client.dispatcher().executorService().shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 连接池统计信息
     */
    public static class ConnectionPoolStats {
        private final int totalConnections;
        private final int idleConnections;
        
        public ConnectionPoolStats(int totalConnections, int idleConnections) {
            this.totalConnections = totalConnections;
            this.idleConnections = idleConnections;
        }
        
        public int getTotalConnections() {
            return totalConnections;
        }
        
        public int getIdleConnections() {
            return idleConnections;
        }
        
        public int getActiveConnections() {
            return totalConnections - idleConnections;
        }
        
        @Override
        public String toString() {
            return String.format("连接池状态: 总连接=%d, 空闲=%d, 活跃=%d", 
                totalConnections, idleConnections, getActiveConnections());
        }
    }
}
