package com.example.controller;

import com.example.model.EventCodeCostTime;
import com.example.service.StatisticsTimeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/statistics")
public class StatisticsTimeController {

    @Autowired
    StatisticsTimeService statisticsTimeService;

    @GetMapping("/all")
    public List<EventCodeCostTime> all(@RequestParam("eventCode") String eventCode) {
        return statisticsTimeService.getTotalCostByEventCode(eventCode, null, null);
    }
}
