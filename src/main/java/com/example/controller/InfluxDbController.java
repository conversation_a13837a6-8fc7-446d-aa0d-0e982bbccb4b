package com.example.controller;

import com.example.common.utils.InfluxDbUtils;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/influxdb")
public class InfluxDbController {

    private static final Logger LOGGER = LoggerFactory.getLogger(InfluxDbController.class);

    @Resource
    InfluxDbUtils influxDbUtils;

    @Value("${spring.influx.database}")
    private String database;

    @GetMapping("/test")
    public void test() {
        LOGGER.info("---开始查询数据---");
        // InfluxDB支持分页查询,因此可以设置分页查询条件
        // 起始页（从0开始）
        int page = 1;
        // 每页条目数
        int pageSize = 10;
        String pageQuery = " LIMIT " + pageSize + " OFFSET " + (page - 1) * pageSize;
        //查询条件暂且为空
        String queryCondition = "";
        // 此处查询所有内容,如果
        String queryCmd = "SELECT * FROM "
                // 要指定从 RetentionPolicyName.measurement中查询指定数据,默认的策略可以不加；
                // + 策略name + "." + measurement
                + "temperature"
                // 添加查询条件(注意查询条件选择tag值,选择field数值会严重拖慢查询速度)
                + queryCondition
                // 查询结果需要按照时间排序
                + " ORDER BY time DESC"
                // 添加分页查询条件
                + pageQuery;

        // 开始查询
        QueryResult queryResult = influxDbUtils.getInfluxDB().query(new Query(queryCmd, database));
        LOGGER.info("原始结果为：{}", queryResult);

        // 获取查询结果
        List<QueryResult.Result> results = queryResult.getResults();
        if (results == null) {
            return;
        }
        // 多个sql用分号隔开，因本次查询只有一个sql，所以取第一个就行
        QueryResult.Result result = results.get(0);
        List<QueryResult.Series> seriesList = result.getSeries();

        for (QueryResult.Series series : seriesList) {
            if (series == null) {
                return;
            }
            LOGGER.info("结果数量为：{}", (series.getValues() == null ? 0 : series.getValues().size()));
            LOGGER.info("colums ==>> {}", series.getColumns());
            LOGGER.info("tags ==>> {}", series.getTags());
            LOGGER.info("name ==>> {}", series.getName());
            LOGGER.info("values ==>> {}", series.getValues());

        }
    }
}
