<?xml version="1.0" encoding="UTF-8" ?>
<configuration>
    <appender name="info" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- <File>logs/tools-info.eslog</File> -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{"yyyy-MM-dd HH:mm:ss,SSS"}^|%p^|%t^|tools^|%X{logid}^|%X{logid_r}^|%logger{0}^|%msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>info</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>logs/%d{yyyyMMdd}-tools-info.eslog</FileNamePattern>
        </rollingPolicy>
    </appender>

    <appender name="warn" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- <File>logs/tools-warn.eslog</File> -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{"yyyy-MM-dd HH:mm:ss,SSS"}^|%p^|%t^|tools^|%X{logid}^|%X{logid_r}^|%logger{0}^|%msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <OnMismatch>DENY</OnMismatch>
            <OnMatch>ACCEPT</OnMatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>logs/%d{yyyyMMdd}-tools-warn.eslog</FileNamePattern>
        </rollingPolicy>
    </appender>

    <appender name="error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- <File>logs/tools-error.eslog</File> -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{"yyyy-MM-dd HH:mm:ss,SSS"}^|%p^|%t^|tools^|%X{logid}^|%X{logid_r}^|%logger{0}^|%msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>logs/%d{yyyyMMdd}-tools-error.eslog</FileNamePattern>
        </rollingPolicy>
    </appender>

    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>${console.log.level}</level>
        </filter>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>%d{"yyyy-MM-dd HH:mm:ss,SSS"}^|%p^|%t^|tools^|%X{logid}^|%X{logid_r}^|%logger{0}^|%msg%n</pattern>
        </layout>
    </appender>

    <logger name="org.apache.axis.ConfigurationException" level="INFO" />

    <logger name ="com" level="info">
        <appender-ref ref="info" />
        <appender-ref ref="warn" />
        <appender-ref ref="error" />
        <appender-ref ref="console" />
    </logger>

    <logger name ="org" level="info">
        <appender-ref ref="info" />
        <appender-ref ref="warn" />
        <appender-ref ref="error" />
        <appender-ref ref="console" />
    </logger>
</configuration>
