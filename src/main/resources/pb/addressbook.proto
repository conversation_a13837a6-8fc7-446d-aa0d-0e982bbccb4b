syntax = "proto3"; // 声明为protobuf 3定义文件
package tutorial;


option optimize_for = SPEED; // SPEED/CODE_SIZE/LITE_RUNTIME
option java_package = "com.example.protobuf.message"; // 声明生成消息类的java包路径
option java_outer_classname = "AddressBookProtos";  // 声明生成消息类的类名

message Person {
  string name = 1;
  int32 id = 2;
  string email = 3;

  enum PhoneType {
    MOBILE = 0;
    HOME = 1;
    WORK = 2;
  }

  message PhoneNumber {
    string number = 1;
    PhoneType type = 2;
  }

  repeated PhoneNumber phones = 4;
}

message AddressBook {
  repeated Person people = 1;
}