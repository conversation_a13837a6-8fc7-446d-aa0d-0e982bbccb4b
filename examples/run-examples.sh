#!/bin/bash

# HTTP负载测试工具示例脚本
# 演示各种使用场景

echo "HTTP负载测试工具示例"
echo "===================="

# 设置基本变量
JAVA_CMD="java -cp ../target/classes com.example.loadtest.LoadTestMain"
TEST_URL="https://httpbin.org"

echo ""
echo "1. 基本GET请求测试 (低负载)"
echo "命令: $JAVA_CMD -u $TEST_URL/get -r 10 -c 5 -d 10"
echo "按Enter继续..."
read

$JAVA_CMD -u $TEST_URL/get -r 10 -c 5 -d 10

echo ""
echo "2. 中等负载测试"
echo "命令: $JAVA_CMD -u $TEST_URL/get -r 100 -c 20 -d 30"
echo "按Enter继续..."
read

$JAVA_CMD -u $TEST_URL/get -r 100 -c 20 -d 30

echo ""
echo "3. POST请求测试"
echo "命令: $JAVA_CMD -u $TEST_URL/post -m POST -H 'Content-Type: application/json' -b '{\"test\":\"data\"}' -r 50 -c 10 -d 20"
echo "按Enter继续..."
read

$JAVA_CMD -u $TEST_URL/post -m POST -H "Content-Type: application/json" -b '{"test":"data","timestamp":"'$(date -Iseconds)'"}' -r 50 -c 10 -d 20

echo ""
echo "4. 固定请求数测试"
echo "命令: $JAVA_CMD -u $TEST_URL/get -n 100 -r 20 -c 5"
echo "按Enter继续..."
read

$JAVA_CMD -u $TEST_URL/get -n 100 -r 20 -c 5

echo ""
echo "5. 带输出文件的测试"
echo "命令: $JAVA_CMD -u $TEST_URL/get -r 30 -c 6 -d 15 -o test-results.csv"
echo "按Enter继续..."
read

$JAVA_CMD -u $TEST_URL/get -r 30 -c 6 -d 15 -o test-results.csv

if [ -f "test-results.csv" ]; then
    echo ""
    echo "测试结果已保存到 test-results.csv"
    echo "前几行内容:"
    head -5 test-results.csv
fi

echo ""
echo "6. 自定义头部测试"
echo "命令: $JAVA_CMD -u $TEST_URL/headers -H 'User-Agent: LoadTester/1.0' -H 'X-Test-Header: example' -r 25 -c 5 -d 10"
echo "按Enter继续..."
read

$JAVA_CMD -u $TEST_URL/headers -H "User-Agent: LoadTester/1.0" -H "X-Test-Header: example" -r 25 -c 5 -d 10

echo ""
echo "7. 高并发测试 (谨慎使用)"
echo "命令: $JAVA_CMD -u $TEST_URL/get -r 500 -c 50 -d 30 --max-connections 200"
echo "警告: 这将产生较高负载，确认要继续吗? (y/N)"
read -n 1 confirm

if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
    echo ""
    $JAVA_CMD -u $TEST_URL/get -r 500 -c 50 -d 30 --max-connections 200
else
    echo ""
    echo "跳过高并发测试"
fi

echo ""
echo "示例演示完成!"
echo ""
echo "更多使用方法请参考:"
echo "  $JAVA_CMD --help"
echo ""
echo "或查看 README.md 文件"
